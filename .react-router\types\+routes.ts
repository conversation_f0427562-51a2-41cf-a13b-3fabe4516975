// Generated by React Router

import "react-router"

declare module "react-router" {
  interface Register {
    pages: Pages
    routeFiles: RouteFiles
  }
}

type Pages = {
  "/": {
    params: {};
  };
  "/auth": {
    params: {};
  };
  "/journal": {
    params: {};
  };
  "/journal/new": {
    params: {};
  };
  "/journal/:id": {
    params: {
      "id": string;
    };
  };
  "/journal/:id/edit": {
    params: {
      "id": string;
    };
  };
  "/echoes": {
    params: {};
  };
};

type RouteFiles = {
  "root.tsx": {
    id: "root";
    page: "/" | "/auth" | "/journal" | "/journal/new" | "/journal/:id" | "/journal/:id/edit" | "/echoes";
  };
  "routes/home.tsx": {
    id: "routes/home";
    page: "/";
  };
  "routes/auth.tsx": {
    id: "routes/auth";
    page: "/auth";
  };
  "routes/journal.tsx": {
    id: "routes/journal";
    page: "/journal";
  };
  "routes/journal.new.tsx": {
    id: "routes/journal.new";
    page: "/journal/new";
  };
  "routes/journal.$id.tsx": {
    id: "routes/journal.$id";
    page: "/journal/:id";
  };
  "routes/journal.$id.edit.tsx": {
    id: "routes/journal.$id.edit";
    page: "/journal/:id/edit";
  };
  "routes/echoes.tsx": {
    id: "routes/echoes";
    page: "/echoes";
  };
};