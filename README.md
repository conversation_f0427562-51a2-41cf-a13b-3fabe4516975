**简体中文** | [English](./README_EN.md)

# 梦的出口 - Dream's Exit

一个诗意的回忆与情感分享空间，基于 React Router v7 和 Supabase 构建。

> "回忆太美所以人才念旧，物是人非谁不是流泪的旁观者"

## 🌟 项目特色

### 时光书简 (Chronoscroll)
- 私密的个人回忆记录空间
- 支持文字、图片、音乐的多媒体记录
- 创建属于自己的"海市蜃楼"
- 完整的 CRUD 操作（创建、查看、编辑、删除）

### 回音长廊 (Echo Gallery)
- 匿名的情感分享空间
- 24小时自动消失机制
- 如"漫天蝶游"般自由表达
- 在无声的共鸣中获得慰藉

## 🛠 技术栈

- **前端框架**: React Router v7
- **包管理器**: pnpm
- **数据库**: Supabase (PostgreSQL)
- **认证**: Supabase Auth
- **样式**: Tailwind CSS
- **图标**: Lucide React
- **类型检查**: TypeScript
- **日期处理**: date-fns
- **工具库**: clsx

## 🚀 快速开始

### 1. 安装依赖
```bash
pnpm install
```

### 2. 配置环境变量
复制 `.env.example` 到 `.env` 并填入你的 Supabase 配置：

```env
VITE_SUPABASE_URL=你的项目URL
VITE_SUPABASE_ANON_KEY=你的anon密钥
```

### 3. 设置 Supabase
1. 在 [Supabase](https://supabase.com) 创建新项目
2. 在 SQL Editor 中执行 `supabase-setup.sql` 脚本
3. 创建存储桶：`chrono-images` 和 `chrono-music`
4. 配置认证设置

详细步骤请参考 `SUPABASE_SETUP.md`

### 4. 启动开发服务器
```bash
pnpm dev
```

访问 http://localhost:5173

## 📦 生产环境构建

创建生产环境构建：

```bash
pnpm build
```

## 🚢 部署

### Docker 部署

使用 Docker 构建和运行：

```bash
docker build -t my-app .

# 运行容器
docker run -p 3000:3000 my-app
```

容器化的应用可以部署到任何支持 Docker 的平台，包括：

- AWS ECS
- Google Cloud Run
- Azure Container Apps
- Digital Ocean App Platform
- Fly.io
- Railway

### 自定义部署

如果你熟悉 Node 应用的部署，内置的应用服务器已经可以用于生产环境。

确保部署 `pnpm build` 的输出内容：

```
├── package.json
├── pnpm-lock.yaml
├── build/
│   ├── client/    # 静态资源
│   └── server/    # 服务端代码
```

## 🎨 样式

本项目已配置好 [Tailwind CSS](https://tailwindcss.com/)，提供简单的默认样式体验。你可以使用任何你喜欢的 CSS 框架。

---

用 ❤️ 和 React Router 构建。
