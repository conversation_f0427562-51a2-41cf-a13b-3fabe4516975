[简体中文](./README.md) | **English**

# Dream's Exit

A poetic space for memories and emotional sharing, built with React Router v7 and Supabase.

> "Memories are too beautiful, that's why people are nostalgic. When things change, who isn't a tearful bystander?"

## 🌟 Features

### Chronoscroll
- Private personal memory recording space
- Support for multimedia records with text, images, and music
- Create your own "mirage"
- Complete CRUD operations (Create, Read, Update, Delete)

### Echo Gallery
- Anonymous emotional sharing space
- 24-hour auto-disappear mechanism
- Express freely like "butterflies dancing in the sky"
- Find solace in silent resonance

## 🛠 Tech Stack

- **Frontend Framework**: React Router v7
- **Package Manager**: pnpm
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **Type Checking**: TypeScript
- **Date Handling**: date-fns
- **Utilities**: clsx

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pnpm install
```

### 2. Configure Environment Variables
Copy `.env.example` to `.env` and fill in your Supabase configuration:

```env
VITE_SUPABASE_URL=your_project_url
VITE_SUPABASE_ANON_KEY=your_anon_key
```

### 3. Set up Supabase
1. Create a new project at [Supabase](https://supabase.com)
2. Execute the `supabase-setup.sql` script in SQL Editor
3. Create storage buckets: `chrono-images` and `chrono-music`
4. Configure authentication settings

For detailed steps, please refer to `SUPABASE_SETUP.md`

### 4. Start Development Server
```bash
pnpm dev
```

Visit http://localhost:5173

## 📦 Production Build

Create a production build:

```bash
pnpm build
```

## 🚢 Deployment

### Docker Deployment

Build and run with Docker:

```bash
docker build -t my-app .

# Run the container
docker run -p 3000:3000 my-app
```

The containerized app can be deployed to any platform that supports Docker, including:

- AWS ECS
- Google Cloud Run
- Azure Container Apps
- Digital Ocean App Platform
- Fly.io
- Railway

### DIY Deployment

If you're familiar with deploying Node applications, the built-in app server is production-ready.

Make sure to deploy the output of `pnpm build`:

```
├── package.json
├── pnpm-lock.yaml
├── build/
│   ├── client/    # Static assets
│   └── server/    # Server code
```

## 🎨 Styling

This project comes with [Tailwind CSS](https://tailwindcss.com/) pre-configured for a simple, default styling experience. You can use any CSS framework you prefer.

---

Built with ❤️ and React Router.