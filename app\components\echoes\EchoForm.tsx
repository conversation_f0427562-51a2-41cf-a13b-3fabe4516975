import { useState } from 'react'
import { useEcho<PERSON> } from '~/hooks/useEchoes'
import { useToast } from '~/components/ui/Toast'
import { Send, Clock } from 'lucide-react'

export function EchoForm() {
  const [content, setContent] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const { createEcho } = useEchoes()
  const { success, error: showError } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!content.trim()) {
      setError('请输入内容')
      return
    }

    if (content.length > 500) {
      setError('内容不能超过500字符')
      return
    }

    setLoading(true)
    setError('')

    const { error } = await createEcho(content.trim())

    if (error) {
      setError(error)
      showError(error)
    } else {
      setContent('')
      success('回音已投递到长廊中')
    }
    
    setLoading(false)
  }

  return (
    <div className="bg-white/80 backdrop-blur-enhanced rounded-2xl p-6 shadow-lg mb-8 hover-lift">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">投递一段回音</h3>
        <p className="text-sm text-gray-600 flex items-center space-x-1">
          <Clock className="w-4 h-4" />
          <span>匿名发布，24小时后自动消失</span>
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            rows={4}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
            placeholder="分享你的思绪、情感或感悟...如漫天蝶游般自由表达"
            maxLength={500}
          />
          <div className="flex justify-between items-center mt-2">
            <div className="text-sm text-gray-500">
              {content.length}/500 字符
            </div>
            {content.length > 450 && (
              <div className="text-sm text-orange-600">
                即将达到字符限制
              </div>
            )}
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm">
            {error}
          </div>
        )}

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={loading || !content.trim()}
            className="flex items-center space-x-2 bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all transform hover:scale-105 btn-ripple"
          >
            <Send className="w-5 h-5" />
            <span>{loading ? '发布中...' : '投递回音'}</span>
          </button>
        </div>
      </form>
    </div>
  )
}
