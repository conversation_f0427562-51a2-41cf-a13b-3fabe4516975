import { Layout } from '~/components/layout/Layout'
import { LoadingSpinner } from '~/components/ui/LoadingSpinner'
import { EmptyState } from '~/components/ui/EmptyState'
import { EchoForm } from '~/components/echoes/EchoForm'
import { EchoCard } from '~/components/echoes/EchoCard'
import { useEchoes } from '~/hooks/useEchoes'
import { MessageCircle, Sparkles, RefreshCw } from 'lucide-react'

export default function EchoesPage() {
  const { echoes, loading, error, refetch } = useEchoes()

  return (
    <Layout>
      <div className="max-w-4xl mx-auto">
        {/* 页面头部 */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <MessageCircle className="w-8 h-8 text-purple-600 animate-float" />
            <h1 className="text-3xl font-bold gradient-text">回音长廊</h1>
            <Sparkles className="w-8 h-8 text-purple-600 animate-float" style={{ animationDelay: '1s' }} />
          </div>
          <p className="text-gray-600 max-w-2xl mx-auto leading-relaxed">
            在这里匿名分享你的思绪与情感，如"漫天蝶游"般自由表达。
            每一段回音都会在24小时后消失，象征着情感的流逝。
            谁不是流泪的旁观者，在无声的共鸣中获得慰藉。
          </p>
        </div>

        {/* 发布表单 */}
        <EchoForm />

        {/* 刷新按钮 */}
        <div className="flex justify-center mb-6">
          <button
            onClick={refetch}
            disabled={loading}
            className="flex items-center space-x-2 text-gray-600 hover:text-purple-600 transition-colors"
          >
            <RefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
            <span>刷新回音</span>
          </button>
        </div>

        {/* 加载状态 */}
        {loading && (
          <div className="flex items-center justify-center py-12">
            <LoadingSpinner color="purple" text="聆听回音中..." />
          </div>
        )}

        {/* 错误状态 */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6 backdrop-blur-enhanced">
            {error}
          </div>
        )}

        {/* 空状态 */}
        {!loading && !error && echoes.length === 0 && (
          <EmptyState
            icon={MessageCircle}
            title="长廊中还很安静"
            description="成为第一个投递回音的人吧"
            className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl"
          />
        )}

        {/* 回音列表 */}
        {!loading && echoes.length > 0 && (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <p className="text-gray-600">
                共有 <span className="font-semibold text-purple-600">{echoes.length}</span> 段回音在长廊中回响
              </p>
            </div>
            
            <div className="grid gap-6">
              {echoes.map((echo) => (
                <EchoCard key={echo.id} echo={echo} />
              ))}
            </div>
          </div>
        )}

        {/* 底部装饰 */}
        <div className="text-center mt-12 py-8 border-t border-gray-200">
          <blockquote className="text-lg text-gray-700 italic leading-relaxed mb-2">
            "物是人非谁不是流泪的旁观者"
          </blockquote>
          <p className="text-gray-500">— 在共鸣中寻找慰藉</p>
        </div>
      </div>
    </Layout>
  )
}
