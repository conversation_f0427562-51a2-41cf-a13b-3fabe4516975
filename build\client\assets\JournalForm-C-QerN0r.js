import{a as t,x as k,o as e}from"./chunk-QMGIS6GS-Dm1jN8z9.js";import{u as S,M as C}from"./useChronoEntries-DsUNOhE7.js";import"./AuthContext-C3ETFuYa.js";import{I as U}from"./image-CJpBvlEq.js";import{X as M}from"./Layout-B-k2_PuX.js";import{c as _}from"./user-BOgs6E2m.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E=[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]],F=_("save",E);function J({entry:r,isEdit:a=!1}){const[i,d]=t.useState(""),[l,m]=t.useState(""),[o,u]=t.useState(""),[x,p]=t.useState(""),[g,h]=t.useState(!1),[b,c]=t.useState(""),{createEntry:v,updateEntry:y}=S(),f=k();t.useEffect(()=>{r&&(d(r.title),m(r.content),u(r.image_url||""),p(r.music_url||""))},[r]);const N=async s=>{if(s.preventDefault(),!i.trim()||!l.trim()){c("标题和内容不能为空");return}h(!0),c("");const j={title:i.trim(),content:l.trim(),image_url:o.trim()||void 0,music_url:x.trim()||void 0};let n;a&&r?n=await y(r.id,j):n=await v(j),n.error?c(n.error):f("/journal"),h(!1)},w=()=>{f("/journal")};return e.jsx("div",{className:"max-w-4xl mx-auto",children:e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl p-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:a?"编辑记录":"创建新记录"}),e.jsx("p",{className:"text-gray-600",children:a?"修改你的珍贵回忆":"记录一个珍贵的回忆瞬间"})]}),e.jsxs("form",{onSubmit:N,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 mb-2",children:"标题 *"}),e.jsx("input",{id:"title",type:"text",value:i,onChange:s=>d(s.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"为这个回忆起个标题...",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"content",className:"block text-sm font-medium text-gray-700 mb-2",children:"内容 *"}),e.jsx("textarea",{id:"content",value:l,onChange:s=>m(s.target.value),rows:12,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",placeholder:"写下你的回忆，记录那些珍贵的瞬间...",required:!0}),e.jsxs("div",{className:"text-sm text-gray-500 mt-1",children:[l.length," 字符"]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"imageUrl",className:"block text-sm font-medium text-gray-700 mb-2",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(U,{className:"w-4 h-4"}),e.jsx("span",{children:"图片链接（可选）"})]})}),e.jsx("input",{id:"imageUrl",type:"url",value:o,onChange:s=>u(s.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"https://example.com/image.jpg"}),o&&e.jsx("div",{className:"mt-3",children:e.jsx("img",{src:o,alt:"预览",className:"w-full max-h-48 object-cover rounded-lg",onError:()=>c("图片链接无效")})})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"musicUrl",className:"block text-sm font-medium text-gray-700 mb-2",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(C,{className:"w-4 h-4"}),e.jsx("span",{children:"音乐链接（可选）"})]})}),e.jsx("input",{id:"musicUrl",type:"url",value:x,onChange:s=>p(s.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"https://example.com/music.mp3"}),e.jsx("div",{className:"text-sm text-gray-500 mt-1",children:"支持 MP3、WAV 等音频格式"})]}),b&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg",children:b}),e.jsxs("div",{className:"flex items-center justify-end space-x-4 pt-6 border-t border-gray-200",children:[e.jsxs("button",{type:"button",onClick:w,className:"flex items-center space-x-2 px-6 py-3 text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-lg transition-colors",children:[e.jsx(M,{className:"w-5 h-5"}),e.jsx("span",{children:"取消"})]}),e.jsxs("button",{type:"submit",disabled:g,className:"flex items-center space-x-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[e.jsx(F,{className:"w-5 h-5"}),e.jsx("span",{children:g?"保存中...":a?"更新记录":"创建记录"})]})]})]})]})})}export{J};
