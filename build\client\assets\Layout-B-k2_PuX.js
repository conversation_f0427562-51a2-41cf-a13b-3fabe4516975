import{a as y,y as j,o as e,t as d}from"./chunk-QMGIS6GS-Dm1jN8z9.js";import{u as f}from"./AuthContext-C3ETFuYa.js";import{c as o,U as m}from"./user-BOgs6E2m.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N=[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]],b=o("book-open",N);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v=[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]],p=o("log-out",v);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const k=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],w=o("menu",k);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M=[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]],_=o("message-circle",M);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],A=o("x",L);function g(s){var n,t,a="";if(typeof s=="string"||typeof s=="number")a+=s;else if(typeof s=="object")if(Array.isArray(s)){var c=s.length;for(n=0;n<c;n++)s[n]&&(t=g(s[n]))&&(a&&(a+=" "),a+=t)}else for(t in s)s[t]&&(a&&(a+=" "),a+=t);return a}function u(){for(var s,n,t=0,a="",c=arguments.length;t<c;t++)(s=arguments[t])&&(n=g(s))&&(a&&(a+=" "),a+=n);return a}function C(){const[s,n]=y.useState(!1),{user:t,signOut:a}=f(),c=j(),x=async()=>{await a()},h=[{name:"时光书简",href:"/journal",icon:b,description:"记录珍贵回忆"},{name:"回音长廊",href:"/echoes",icon:_,description:"匿名情感分享"}];return e.jsx("nav",{className:"bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"flex justify-between items-center h-16",children:[e.jsxs(d,{to:"/",className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:e.jsx("span",{className:"text-white font-bold text-sm",children:"梦"})}),e.jsx("span",{className:"text-xl font-bold text-gray-900",children:"梦的出口"})]}),e.jsx("div",{className:"hidden md:flex items-center space-x-8",children:h.map(r=>{const l=r.icon,i=c.pathname.startsWith(r.href);return e.jsxs(d,{to:r.href,className:u("flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors",i?"bg-blue-50 text-blue-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"),children:[e.jsx(l,{className:"w-5 h-5"}),e.jsx("span",{className:"font-medium",children:r.name})]},r.href)})}),e.jsx("div",{className:"hidden md:flex items-center space-x-4",children:t&&e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("div",{className:"flex items-center space-x-2 text-gray-700",children:[e.jsx(m,{className:"w-5 h-5"}),e.jsx("span",{className:"text-sm",children:t.email})]}),e.jsxs("button",{onClick:x,className:"flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors",children:[e.jsx(p,{className:"w-5 h-5"}),e.jsx("span",{children:"登出"})]})]})}),e.jsx("div",{className:"md:hidden",children:e.jsx("button",{onClick:()=>n(!s),className:"p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-50",children:s?e.jsx(A,{className:"w-6 h-6"}):e.jsx(w,{className:"w-6 h-6"})})})]}),s&&e.jsx("div",{className:"md:hidden py-4 border-t border-gray-200",children:e.jsxs("div",{className:"space-y-2",children:[h.map(r=>{const l=r.icon,i=c.pathname.startsWith(r.href);return e.jsxs(d,{to:r.href,onClick:()=>n(!1),className:u("flex items-center space-x-3 px-3 py-3 rounded-lg transition-colors",i?"bg-blue-50 text-blue-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"),children:[e.jsx(l,{className:"w-5 h-5"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:r.name}),e.jsx("div",{className:"text-sm text-gray-500",children:r.description})]})]},r.href)}),t&&e.jsxs("div",{className:"pt-4 border-t border-gray-200",children:[e.jsxs("div",{className:"flex items-center space-x-3 px-3 py-2 text-gray-700",children:[e.jsx(m,{className:"w-5 h-5"}),e.jsx("span",{className:"text-sm",children:t.email})]}),e.jsxs("button",{onClick:x,className:"w-full flex items-center space-x-3 px-3 py-3 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors",children:[e.jsx(p,{className:"w-5 h-5"}),e.jsx("span",{children:"登出"})]})]})]})})]})})}function z({children:s}){return e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50",children:[e.jsx(C,{}),e.jsx("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:s})]})}export{b as B,z as L,_ as M,A as X,u as c};
