import{o as e,N as n}from"./chunk-QMGIS6GS-Dm1jN8z9.js";import{u as a}from"./AuthContext-C3ETFuYa.js";function c({children:r}){const{user:s,loading:l,error:t}=a();return l?e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-600",children:"正在验证身份..."})]})}):t?e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsxs("div",{className:"text-center max-w-md mx-auto p-6",children:[e.jsxs("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4",children:[e.jsx("p",{className:"text-yellow-800",children:"身份验证服务暂时不可用"}),e.jsx("p",{className:"text-sm text-yellow-600 mt-2",children:t})]}),e.jsx("button",{onClick:()=>window.location.reload(),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"重试"})]})}):s?e.jsx(e.Fragment,{children:r}):e.jsx(n,{to:"/auth",replace:!0})}export{c as P};
