import{o as e,a as d}from"./chunk-QMGIS6GS-Dm1jN8z9.js";import{c as l,X as f}from"./Layout-B-k2_PuX.js";import{c as x}from"./user-BOgs6E2m.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],p=x("circle-alert",h);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const b=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],w=x("circle-check-big",b);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const j=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]],N=x("circle-x",j);function z({size:t="md",color:a="blue",text:r}){const c={sm:"w-6 h-6",md:"w-12 h-12",lg:"w-16 h-16"},o={blue:"border-blue-600",purple:"border-purple-600",gray:"border-gray-600"};return e.jsxs("div",{className:"flex flex-col items-center justify-center space-y-4",children:[e.jsx("div",{className:l("animate-spin rounded-full border-b-2",c[t],o[a])}),r&&e.jsx("p",{className:"text-gray-600 text-sm animate-pulse",children:r})]})}function _({icon:t,title:a,description:r,action:c,className:o=""}){return e.jsxs("div",{className:`text-center py-12 ${o}`,children:[e.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mx-auto mb-6 animate-float",children:e.jsx(t,{className:"w-8 h-8 text-gray-400"})}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:a}),e.jsx("p",{className:"text-gray-600 mb-6 max-w-md mx-auto",children:r}),c&&c]})}function k({type:t,message:a,onClose:r,duration:c=5e3}){const[o,m]=d.useState(!0);d.useEffect(()=>{const i=setTimeout(()=>{m(!1),setTimeout(r,300)},c);return()=>clearTimeout(i)},[c,r]);const u={success:w,error:N,warning:p},y={success:"bg-green-50 border-green-200 text-green-800",error:"bg-red-50 border-red-200 text-red-800",warning:"bg-yellow-50 border-yellow-200 text-yellow-800"},s={success:"text-green-500",error:"text-red-500",warning:"text-yellow-500"},n=u[t];return e.jsx("div",{className:l("fixed top-4 right-4 z-50 max-w-sm w-full transition-all duration-300 transform",o?"translate-x-0 opacity-100":"translate-x-full opacity-0"),children:e.jsxs("div",{className:l("flex items-start space-x-3 p-4 rounded-lg border shadow-lg backdrop-blur-sm",y[t]),children:[e.jsx(n,{className:l("w-5 h-5 mt-0.5 flex-shrink-0",s[t])}),e.jsx("div",{className:"flex-1",children:e.jsx("p",{className:"text-sm font-medium",children:a})}),e.jsx("button",{onClick:()=>{m(!1),setTimeout(r,300)},className:"flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors",children:e.jsx(f,{className:"w-4 h-4"})})]})})}function E(){const[t,a]=d.useState([]),r=(s,n)=>{const i=Math.random().toString(36).substr(2,9);a(g=>[...g,{id:i,type:s,message:n}])},c=s=>{a(n=>n.filter(i=>i.id!==s))};return{success:s=>r("success",s),error:s=>r("error",s),warning:s=>r("warning",s),ToastContainer:()=>e.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:t.map(s=>e.jsx(k,{type:s.type,message:s.message,onClose:()=>c(s.id)},s.id))})}}export{_ as E,z as L,E as u};
