import{a as s,o as e,w as P,N as E}from"./chunk-QMGIS6GS-Dm1jN8z9.js";import{u as y}from"./AuthContext-C3ETFuYa.js";import{c as j,U as L}from"./user-BOgs6E2m.js";import{E as N}from"./eye-BYrV7Kom.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q=[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],v=j("eye-off",q);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const F=[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]],g=j("lock",F);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M=[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]],S=j("mail",M);function _({onToggleMode:u}){const[r,c]=s.useState(""),[t,m]=s.useState(""),[i,p]=s.useState(!1),[a,h]=s.useState(!1),[x,l]=s.useState(""),{signIn:b}=y(),d=async o=>{o.preventDefault(),h(!0),l("");const{error:f}=await b(r,t);f&&l(f.message),h(!1)};return e.jsxs("div",{className:"w-full max-w-md mx-auto",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"欢迎回到梦境"}),e.jsx("p",{className:"text-gray-600",children:"登录你的梦的出口"})]}),e.jsxs("form",{onSubmit:d,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱地址"}),e.jsxs("div",{className:"relative",children:[e.jsx(S,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),e.jsx("input",{id:"email",type:"email",value:r,onChange:o=>c(o.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入邮箱地址",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"密码"}),e.jsxs("div",{className:"relative",children:[e.jsx(g,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),e.jsx("input",{id:"password",type:i?"text":"password",value:t,onChange:o=>m(o.target.value),className:"w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入密码",required:!0}),e.jsx("button",{type:"button",onClick:()=>p(!i),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:i?e.jsx(v,{className:"w-5 h-5"}):e.jsx(N,{className:"w-5 h-5"})})]})]}),x&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg",children:x}),e.jsx("button",{type:"submit",disabled:a,className:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:a?"登录中...":"登录"})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsxs("p",{className:"text-gray-600",children:["还没有账户？"," ",e.jsx("button",{onClick:u,className:"text-blue-600 hover:text-blue-700 font-medium",children:"立即注册"})]})})]})}function U({onToggleMode:u}){const[r,c]=s.useState(""),[t,m]=s.useState(""),[i,p]=s.useState(""),[a,h]=s.useState(!1),[x,l]=s.useState(!1),[b,d]=s.useState(""),[o,f]=s.useState(!1),{signUp:k}=y(),C=async n=>{if(n.preventDefault(),l(!0),d(""),t!==i){d("密码确认不匹配"),l(!1);return}if(t.length<6){d("密码长度至少为6位"),l(!1);return}const{error:w}=await k(r,t);w?d(w.message):f(!0),l(!1)};return o?e.jsx("div",{className:"w-full max-w-md mx-auto text-center",children:e.jsxs("div",{className:"bg-green-50 border border-green-200 text-green-700 px-6 py-8 rounded-lg",children:[e.jsx(L,{className:"w-16 h-16 mx-auto mb-4 text-green-500"}),e.jsx("h2",{className:"text-xl font-semibold mb-2",children:"注册成功！"}),e.jsx("p",{className:"mb-4",children:"我们已向您的邮箱发送了确认邮件，请点击邮件中的链接来激活您的账户。"}),e.jsx("button",{onClick:u,className:"text-blue-600 hover:text-blue-700 font-medium",children:"返回登录"})]})}):e.jsxs("div",{className:"w-full max-w-md mx-auto",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"踏入梦境"}),e.jsx("p",{className:"text-gray-600",children:"创建你的梦的出口账户"})]}),e.jsxs("form",{onSubmit:C,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱地址"}),e.jsxs("div",{className:"relative",children:[e.jsx(S,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),e.jsx("input",{id:"email",type:"email",value:r,onChange:n=>c(n.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入邮箱地址",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"密码"}),e.jsxs("div",{className:"relative",children:[e.jsx(g,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),e.jsx("input",{id:"password",type:a?"text":"password",value:t,onChange:n=>m(n.target.value),className:"w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入密码（至少6位）",required:!0}),e.jsx("button",{type:"button",onClick:()=>h(!a),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:a?e.jsx(v,{className:"w-5 h-5"}):e.jsx(N,{className:"w-5 h-5"})})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"确认密码"}),e.jsxs("div",{className:"relative",children:[e.jsx(g,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),e.jsx("input",{id:"confirmPassword",type:a?"text":"password",value:i,onChange:n=>p(n.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请再次输入密码",required:!0})]})]}),b&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg",children:b}),e.jsx("button",{type:"submit",disabled:x,className:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:x?"注册中...":"注册"})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsxs("p",{className:"text-gray-600",children:["已有账户？"," ",e.jsx("button",{onClick:u,className:"text-blue-600 hover:text-blue-700 font-medium",children:"立即登录"})]})})]})}const $=P(function(){const[r,c]=s.useState(!0),{user:t,loading:m}=y();return m?e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):t?e.jsx(E,{to:"/",replace:!0}):e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4",children:e.jsxs("div",{className:"w-full max-w-md",children:[e.jsx("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:r?e.jsx(_,{onToggleMode:()=>c(!1)}):e.jsx(U,{onToggleMode:()=>c(!0)})}),e.jsx("div",{className:"text-center mt-8 text-gray-500 text-sm",children:e.jsx("p",{children:"梦的出口 - 一个诗意的回忆与情感分享空间"})})]})})});export{$ as default};
