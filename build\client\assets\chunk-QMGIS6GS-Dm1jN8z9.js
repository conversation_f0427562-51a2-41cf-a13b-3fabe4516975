var Yt={exports:{}},rt={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Fr;function Ea(){if(Fr)return rt;Fr=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.fragment");function r(a,n,o){var l=null;if(o!==void 0&&(l=""+o),n.key!==void 0&&(l=""+n.key),"key"in n){o={};for(var s in n)s!=="key"&&(o[s]=n[s])}else o=n;return n=o.ref,{$$typeof:e,type:a,key:l,ref:n!==void 0?n:null,props:o}}return rt.Fragment=t,rt.jsx=r,rt.jsxs=r,rt}var jr;function Ra(){return jr||(jr=1,Yt.exports=Ea()),Yt.exports}var fl=Ra(),Vt={exports:{}},W={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hr;function ba(){if(Hr)return W;Hr=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),n=Symbol.for("react.profiler"),o=Symbol.for("react.consumer"),l=Symbol.for("react.context"),s=Symbol.for("react.forward_ref"),i=Symbol.for("react.suspense"),u=Symbol.for("react.memo"),c=Symbol.for("react.lazy"),p=Symbol.iterator;function f(m){return m===null||typeof m!="object"?null:(m=p&&m[p]||m["@@iterator"],typeof m=="function"?m:null)}var g={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},w=Object.assign,b={};function S(m,L,N){this.props=m,this.context=L,this.refs=b,this.updater=N||g}S.prototype.isReactComponent={},S.prototype.setState=function(m,L){if(typeof m!="object"&&typeof m!="function"&&m!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,m,L,"setState")},S.prototype.forceUpdate=function(m){this.updater.enqueueForceUpdate(this,m,"forceUpdate")};function R(){}R.prototype=S.prototype;function O(m,L,N){this.props=m,this.context=L,this.refs=b,this.updater=N||g}var T=O.prototype=new R;T.constructor=O,w(T,S.prototype),T.isPureReactComponent=!0;var M=Array.isArray,x={H:null,A:null,T:null,S:null,V:null},y=Object.prototype.hasOwnProperty;function D(m,L,N,k,Y,ee){return N=ee.ref,{$$typeof:e,type:m,key:L,ref:N!==void 0?N:null,props:ee}}function U(m,L){return D(m.type,L,void 0,void 0,void 0,m.props)}function j(m){return typeof m=="object"&&m!==null&&m.$$typeof===e}function K(m){var L={"=":"=0",":":"=2"};return"$"+m.replace(/[=:]/g,function(N){return L[N]})}var ne=/\/+/g;function se(m,L){return typeof m=="object"&&m!==null&&m.key!=null?K(""+m.key):L.toString(36)}function oe(){}function X(m){switch(m.status){case"fulfilled":return m.value;case"rejected":throw m.reason;default:switch(typeof m.status=="string"?m.then(oe,oe):(m.status="pending",m.then(function(L){m.status==="pending"&&(m.status="fulfilled",m.value=L)},function(L){m.status==="pending"&&(m.status="rejected",m.reason=L)})),m.status){case"fulfilled":return m.value;case"rejected":throw m.reason}}throw m}function Q(m,L,N,k,Y){var ee=typeof m;(ee==="undefined"||ee==="boolean")&&(m=null);var z=!1;if(m===null)z=!0;else switch(ee){case"bigint":case"string":case"number":z=!0;break;case"object":switch(m.$$typeof){case e:case t:z=!0;break;case c:return z=m._init,Q(z(m._payload),L,N,k,Y)}}if(z)return Y=Y(m),z=k===""?"."+se(m,0):k,M(Y)?(N="",z!=null&&(N=z.replace(ne,"$&/")+"/"),Q(Y,L,N,"",function(jt){return jt})):Y!=null&&(j(Y)&&(Y=U(Y,N+(Y.key==null||m&&m.key===Y.key?"":(""+Y.key).replace(ne,"$&/")+"/")+z)),L.push(Y)),1;z=0;var Le=k===""?".":k+":";if(M(m))for(var ae=0;ae<m.length;ae++)k=m[ae],ee=Le+se(k,ae),z+=Q(k,L,N,ee,Y);else if(ae=f(m),typeof ae=="function")for(m=ae.call(m),ae=0;!(k=m.next()).done;)k=k.value,ee=Le+se(k,ae++),z+=Q(k,L,N,ee,Y);else if(ee==="object"){if(typeof m.then=="function")return Q(X(m),L,N,k,Y);throw L=String(m),Error("Objects are not valid as a React child (found: "+(L==="[object Object]"?"object with keys {"+Object.keys(m).join(", ")+"}":L)+"). If you meant to render a collection of children, use an array instead.")}return z}function G(m,L,N){if(m==null)return m;var k=[],Y=0;return Q(m,k,"","",function(ee){return L.call(N,ee,Y++)}),k}function ue(m){if(m._status===-1){var L=m._result;L=L(),L.then(function(N){(m._status===0||m._status===-1)&&(m._status=1,m._result=N)},function(N){(m._status===0||m._status===-1)&&(m._status=2,m._result=N)}),m._status===-1&&(m._status=0,m._result=L)}if(m._status===1)return m._result.default;throw m._result}var de=typeof reportError=="function"?reportError:function(m){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var L=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof m=="object"&&m!==null&&typeof m.message=="string"?String(m.message):String(m),error:m});if(!window.dispatchEvent(L))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",m);return}console.error(m)};function ce(){}return W.Children={map:G,forEach:function(m,L,N){G(m,function(){L.apply(this,arguments)},N)},count:function(m){var L=0;return G(m,function(){L++}),L},toArray:function(m){return G(m,function(L){return L})||[]},only:function(m){if(!j(m))throw Error("React.Children.only expected to receive a single React element child.");return m}},W.Component=S,W.Fragment=r,W.Profiler=n,W.PureComponent=O,W.StrictMode=a,W.Suspense=i,W.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=x,W.__COMPILER_RUNTIME={__proto__:null,c:function(m){return x.H.useMemoCache(m)}},W.cache=function(m){return function(){return m.apply(null,arguments)}},W.cloneElement=function(m,L,N){if(m==null)throw Error("The argument must be a React element, but you passed "+m+".");var k=w({},m.props),Y=m.key,ee=void 0;if(L!=null)for(z in L.ref!==void 0&&(ee=void 0),L.key!==void 0&&(Y=""+L.key),L)!y.call(L,z)||z==="key"||z==="__self"||z==="__source"||z==="ref"&&L.ref===void 0||(k[z]=L[z]);var z=arguments.length-2;if(z===1)k.children=N;else if(1<z){for(var Le=Array(z),ae=0;ae<z;ae++)Le[ae]=arguments[ae+2];k.children=Le}return D(m.type,Y,void 0,void 0,ee,k)},W.createContext=function(m){return m={$$typeof:l,_currentValue:m,_currentValue2:m,_threadCount:0,Provider:null,Consumer:null},m.Provider=m,m.Consumer={$$typeof:o,_context:m},m},W.createElement=function(m,L,N){var k,Y={},ee=null;if(L!=null)for(k in L.key!==void 0&&(ee=""+L.key),L)y.call(L,k)&&k!=="key"&&k!=="__self"&&k!=="__source"&&(Y[k]=L[k]);var z=arguments.length-2;if(z===1)Y.children=N;else if(1<z){for(var Le=Array(z),ae=0;ae<z;ae++)Le[ae]=arguments[ae+2];Y.children=Le}if(m&&m.defaultProps)for(k in z=m.defaultProps,z)Y[k]===void 0&&(Y[k]=z[k]);return D(m,ee,void 0,void 0,null,Y)},W.createRef=function(){return{current:null}},W.forwardRef=function(m){return{$$typeof:s,render:m}},W.isValidElement=j,W.lazy=function(m){return{$$typeof:c,_payload:{_status:-1,_result:m},_init:ue}},W.memo=function(m,L){return{$$typeof:u,type:m,compare:L===void 0?null:L}},W.startTransition=function(m){var L=x.T,N={};x.T=N;try{var k=m(),Y=x.S;Y!==null&&Y(N,k),typeof k=="object"&&k!==null&&typeof k.then=="function"&&k.then(ce,de)}catch(ee){de(ee)}finally{x.T=L}},W.unstable_useCacheRefresh=function(){return x.H.useCacheRefresh()},W.use=function(m){return x.H.use(m)},W.useActionState=function(m,L,N){return x.H.useActionState(m,L,N)},W.useCallback=function(m,L){return x.H.useCallback(m,L)},W.useContext=function(m){return x.H.useContext(m)},W.useDebugValue=function(){},W.useDeferredValue=function(m,L){return x.H.useDeferredValue(m,L)},W.useEffect=function(m,L,N){var k=x.H;if(typeof N=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return k.useEffect(m,L)},W.useId=function(){return x.H.useId()},W.useImperativeHandle=function(m,L,N){return x.H.useImperativeHandle(m,L,N)},W.useInsertionEffect=function(m,L){return x.H.useInsertionEffect(m,L)},W.useLayoutEffect=function(m,L){return x.H.useLayoutEffect(m,L)},W.useMemo=function(m,L){return x.H.useMemo(m,L)},W.useOptimistic=function(m,L){return x.H.useOptimistic(m,L)},W.useReducer=function(m,L,N){return x.H.useReducer(m,L,N)},W.useRef=function(m){return x.H.useRef(m)},W.useState=function(m){return x.H.useState(m)},W.useSyncExternalStore=function(m,L,N){return x.H.useSyncExternalStore(m,L,N)},W.useTransition=function(){return x.H.useTransition()},W.version="19.1.0",W}var Ur;function Sa(){return Ur||(Ur=1,Vt.exports=ba()),Vt.exports}var h=Sa(),nt={},zr;function xa(){if(zr)return nt;zr=1,Object.defineProperty(nt,"__esModule",{value:!0}),nt.parse=l,nt.serialize=u;const e=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,t=/^[\u0021-\u003A\u003C-\u007E]*$/,r=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,a=/^[\u0020-\u003A\u003D-\u007E]*$/,n=Object.prototype.toString,o=(()=>{const f=function(){};return f.prototype=Object.create(null),f})();function l(f,g){const w=new o,b=f.length;if(b<2)return w;const S=(g==null?void 0:g.decode)||c;let R=0;do{const O=f.indexOf("=",R);if(O===-1)break;const T=f.indexOf(";",R),M=T===-1?b:T;if(O>M){R=f.lastIndexOf(";",O-1)+1;continue}const x=s(f,R,O),y=i(f,O,x),D=f.slice(x,y);if(w[D]===void 0){let U=s(f,O+1,M),j=i(f,M,U);const K=S(f.slice(U,j));w[D]=K}R=M+1}while(R<b);return w}function s(f,g,w){do{const b=f.charCodeAt(g);if(b!==32&&b!==9)return g}while(++g<w);return w}function i(f,g,w){for(;g>w;){const b=f.charCodeAt(--g);if(b!==32&&b!==9)return g+1}return w}function u(f,g,w){const b=(w==null?void 0:w.encode)||encodeURIComponent;if(!e.test(f))throw new TypeError(`argument name is invalid: ${f}`);const S=b(g);if(!t.test(S))throw new TypeError(`argument val is invalid: ${g}`);let R=f+"="+S;if(!w)return R;if(w.maxAge!==void 0){if(!Number.isInteger(w.maxAge))throw new TypeError(`option maxAge is invalid: ${w.maxAge}`);R+="; Max-Age="+w.maxAge}if(w.domain){if(!r.test(w.domain))throw new TypeError(`option domain is invalid: ${w.domain}`);R+="; Domain="+w.domain}if(w.path){if(!a.test(w.path))throw new TypeError(`option path is invalid: ${w.path}`);R+="; Path="+w.path}if(w.expires){if(!p(w.expires)||!Number.isFinite(w.expires.valueOf()))throw new TypeError(`option expires is invalid: ${w.expires}`);R+="; Expires="+w.expires.toUTCString()}if(w.httpOnly&&(R+="; HttpOnly"),w.secure&&(R+="; Secure"),w.partitioned&&(R+="; Partitioned"),w.priority)switch(typeof w.priority=="string"?w.priority.toLowerCase():void 0){case"low":R+="; Priority=Low";break;case"medium":R+="; Priority=Medium";break;case"high":R+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${w.priority}`)}if(w.sameSite)switch(typeof w.sameSite=="string"?w.sameSite.toLowerCase():w.sameSite){case!0:case"strict":R+="; SameSite=Strict";break;case"lax":R+="; SameSite=Lax";break;case"none":R+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${w.sameSite}`)}return R}function c(f){if(f.indexOf("%")===-1)return f;try{return decodeURIComponent(f)}catch{return f}}function p(f){return n.call(f)==="[object Date]"}return nt}xa();/**
 * react-router v7.6.3
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var pn=e=>{throw TypeError(e)},La=(e,t,r)=>t.has(e)||pn("Cannot "+r),Jt=(e,t,r)=>(La(e,t,"read from private field"),r?r.call(e):t.get(e)),Ca=(e,t,r)=>t.has(e)?pn("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),Br="popstate";function hl(e={}){function t(a,n){let{pathname:o,search:l,hash:s}=a.location;return ut("",{pathname:o,search:l,hash:s},n.state&&n.state.usr||null,n.state&&n.state.key||"default")}function r(a,n){return typeof n=="string"?n:ke(n)}return Ta(t,r,null,e)}function J(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function re(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Pa(){return Math.random().toString(36).substring(2,10)}function Wr(e,t){return{usr:e.state,key:e.key,idx:t}}function ut(e,t,r=null,a){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?Ae(t):t,state:r,key:t&&t.key||a||Pa()}}function ke({pathname:e="/",search:t="",hash:r=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function Ae(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substring(r),e=e.substring(0,r));let a=e.indexOf("?");a>=0&&(t.search=e.substring(a),e=e.substring(0,a)),e&&(t.pathname=e)}return t}function Ta(e,t,r,a={}){let{window:n=document.defaultView,v5Compat:o=!1}=a,l=n.history,s="POP",i=null,u=c();u==null&&(u=0,l.replaceState({...l.state,idx:u},""));function c(){return(l.state||{idx:null}).idx}function p(){s="POP";let S=c(),R=S==null?null:S-u;u=S,i&&i({action:s,location:b.location,delta:R})}function f(S,R){s="PUSH";let O=ut(b.location,S,R);u=c()+1;let T=Wr(O,u),M=b.createHref(O);try{l.pushState(T,"",M)}catch(x){if(x instanceof DOMException&&x.name==="DataCloneError")throw x;n.location.assign(M)}o&&i&&i({action:s,location:b.location,delta:1})}function g(S,R){s="REPLACE";let O=ut(b.location,S,R);u=c();let T=Wr(O,u),M=b.createHref(O);l.replaceState(T,"",M),o&&i&&i({action:s,location:b.location,delta:0})}function w(S){return yn(S)}let b={get action(){return s},get location(){return e(n,l)},listen(S){if(i)throw new Error("A history only accepts one active listener");return n.addEventListener(Br,p),i=S,()=>{n.removeEventListener(Br,p),i=null}},createHref(S){return t(n,S)},createURL:w,encodeLocation(S){let R=w(S);return{pathname:R.pathname,search:R.search,hash:R.hash}},push:f,replace:g,go(S){return l.go(S)}};return b}function yn(e,t=!1){let r="http://localhost";typeof window<"u"&&(r=window.location.origin!=="null"?window.location.origin:window.location.href),J(r,"No window.location.(origin|href) available to create URL");let a=typeof e=="string"?e:ke(e);return a=a.replace(/ $/,"%20"),!t&&a.startsWith("//")&&(a=r+a),new URL(a,r)}var lt,Yr=class{constructor(e){if(Ca(this,lt,new Map),e)for(let[t,r]of e)this.set(t,r)}get(e){if(Jt(this,lt).has(e))return Jt(this,lt).get(e);if(e.defaultValue!==void 0)return e.defaultValue;throw new Error("No value found for context")}set(e,t){Jt(this,lt).set(e,t)}};lt=new WeakMap;var Ma=new Set(["lazy","caseSensitive","path","id","index","children"]);function _a(e){return Ma.has(e)}var Da=new Set(["lazy","caseSensitive","path","id","index","unstable_middleware","children"]);function Oa(e){return Da.has(e)}function ka(e){return e.index===!0}function Mt(e,t,r=[],a={}){return e.map((n,o)=>{let l=[...r,String(o)],s=typeof n.id=="string"?n.id:l.join("-");if(J(n.index!==!0||!n.children,"Cannot specify children on an index route"),J(!a[s],`Found a route id collision on id "${s}".  Route id's must be globally unique within Data Router usages`),ka(n)){let i={...n,...t(n),id:s};return a[s]=i,i}else{let i={...n,...t(n),id:s,children:void 0};return a[s]=i,n.children&&(i.children=Mt(n.children,t,l,a)),i}})}function Ce(e,t,r="/"){return xt(e,t,r,!1)}function xt(e,t,r,a){let n=typeof t=="string"?Ae(t):t,o=ge(n.pathname||"/",r);if(o==null)return null;let l=gn(e);Na(l);let s=null;for(let i=0;s==null&&i<l.length;++i){let u=Wa(o);s=Ba(l[i],u,a)}return s}function vn(e,t){let{route:r,pathname:a,params:n}=e;return{id:r.id,pathname:a,params:n,data:t[r.id],handle:r.handle}}function gn(e,t=[],r=[],a=""){let n=(o,l,s)=>{let i={relativePath:s===void 0?o.path||"":s,caseSensitive:o.caseSensitive===!0,childrenIndex:l,route:o};i.relativePath.startsWith("/")&&(J(i.relativePath.startsWith(a),`Absolute route path "${i.relativePath}" nested under path "${a}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),i.relativePath=i.relativePath.slice(a.length));let u=Pe([a,i.relativePath]),c=r.concat(i);o.children&&o.children.length>0&&(J(o.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${u}".`),gn(o.children,t,c,u)),!(o.path==null&&!o.index)&&t.push({path:u,score:Ua(u,o.index),routesMeta:c})};return e.forEach((o,l)=>{var s;if(o.path===""||!((s=o.path)!=null&&s.includes("?")))n(o,l);else for(let i of wn(o.path))n(o,l,i)}),t}function wn(e){let t=e.split("/");if(t.length===0)return[];let[r,...a]=t,n=r.endsWith("?"),o=r.replace(/\?$/,"");if(a.length===0)return n?[o,""]:[o];let l=wn(a.join("/")),s=[];return s.push(...l.map(i=>i===""?o:[o,i].join("/"))),n&&s.push(...l),s.map(i=>e.startsWith("/")&&i===""?"/":i)}function Na(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:za(t.routesMeta.map(a=>a.childrenIndex),r.routesMeta.map(a=>a.childrenIndex)))}var Aa=/^:[\w-]+$/,Ia=3,$a=2,Fa=1,ja=10,Ha=-2,Vr=e=>e==="*";function Ua(e,t){let r=e.split("/"),a=r.length;return r.some(Vr)&&(a+=Ha),t&&(a+=$a),r.filter(n=>!Vr(n)).reduce((n,o)=>n+(Aa.test(o)?Ia:o===""?Fa:ja),a)}function za(e,t){return e.length===t.length&&e.slice(0,-1).every((a,n)=>a===t[n])?e[e.length-1]-t[t.length-1]:0}function Ba(e,t,r=!1){let{routesMeta:a}=e,n={},o="/",l=[];for(let s=0;s<a.length;++s){let i=a[s],u=s===a.length-1,c=o==="/"?t:t.slice(o.length)||"/",p=_t({path:i.relativePath,caseSensitive:i.caseSensitive,end:u},c),f=i.route;if(!p&&u&&r&&!a[a.length-1].route.index&&(p=_t({path:i.relativePath,caseSensitive:i.caseSensitive,end:!1},c)),!p)return null;Object.assign(n,p.params),l.push({params:n,pathname:Pe([o,p.pathname]),pathnameBase:Ja(Pe([o,p.pathnameBase])),route:f}),p.pathnameBase!=="/"&&(o=Pe([o,p.pathnameBase]))}return l}function _t(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,a]=En(e.path,e.caseSensitive,e.end),n=t.match(r);if(!n)return null;let o=n[0],l=o.replace(/(.)\/+$/,"$1"),s=n.slice(1);return{params:a.reduce((u,{paramName:c,isOptional:p},f)=>{if(c==="*"){let w=s[f]||"";l=o.slice(0,o.length-w.length).replace(/(.)\/+$/,"$1")}const g=s[f];return p&&!g?u[c]=void 0:u[c]=(g||"").replace(/%2F/g,"/"),u},{}),pathname:o,pathnameBase:l,pattern:e}}function En(e,t=!1,r=!0){re(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let a=[],n="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(l,s,i)=>(a.push({paramName:s,isOptional:i!=null}),i?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(a.push({paramName:"*"}),n+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?n+="\\/*$":e!==""&&e!=="/"&&(n+="(?:(?=\\/|$))"),[new RegExp(n,t?void 0:"i"),a]}function Wa(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return re(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function ge(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,a=e.charAt(r);return a&&a!=="/"?null:e.slice(r)||"/"}function Ya(e,t="/"){let{pathname:r,search:a="",hash:n=""}=typeof e=="string"?Ae(e):e;return{pathname:r?r.startsWith("/")?r:Va(r,t):t,search:Ga(a),hash:Xa(n)}}function Va(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(n=>{n===".."?r.length>1&&r.pop():n!=="."&&r.push(n)}),r.length>1?r.join("/"):"/"}function Gt(e,t,r,a){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(a)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function Rn(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function kt(e){let t=Rn(e);return t.map((r,a)=>a===t.length-1?r.pathname:r.pathnameBase)}function Nt(e,t,r,a=!1){let n;typeof e=="string"?n=Ae(e):(n={...e},J(!n.pathname||!n.pathname.includes("?"),Gt("?","pathname","search",n)),J(!n.pathname||!n.pathname.includes("#"),Gt("#","pathname","hash",n)),J(!n.search||!n.search.includes("#"),Gt("#","search","hash",n)));let o=e===""||n.pathname==="",l=o?"/":n.pathname,s;if(l==null)s=r;else{let p=t.length-1;if(!a&&l.startsWith("..")){let f=l.split("/");for(;f[0]==="..";)f.shift(),p-=1;n.pathname=f.join("/")}s=p>=0?t[p]:"/"}let i=Ya(n,s),u=l&&l!=="/"&&l.endsWith("/"),c=(o||l===".")&&r.endsWith("/");return!i.pathname.endsWith("/")&&(u||c)&&(i.pathname+="/"),i}var Pe=e=>e.join("/").replace(/\/\/+/g,"/"),Ja=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Ga=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Xa=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e,qa=class{constructor(e,t){this.type="DataWithResponseInit",this.data=e,this.init=t||null}};function Ka(e,t){return new qa(e,typeof t=="number"?{status:t}:t)}var Qa=(e,t=302)=>{let r=t;typeof r=="number"?r={status:r}:typeof r.status>"u"&&(r.status=302);let a=new Headers(r.headers);return a.set("Location",e),new Response(null,{...r,headers:a})},Ne=class{constructor(e,t,r,a=!1){this.status=e,this.statusText=t||"",this.internal=a,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}};function ze(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var bn=["POST","PUT","PATCH","DELETE"],Za=new Set(bn),eo=["GET",...bn],to=new Set(eo),ro=new Set([301,302,303,307,308]),no=new Set([307,308]),Xt={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},ao={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},at={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},dr=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,oo=e=>({hasErrorBoundary:!!e.hasErrorBoundary}),Sn="remix-router-transitions",xn=Symbol("ResetLoaderData");function ml(e){const t=e.window?e.window:typeof window<"u"?window:void 0,r=typeof t<"u"&&typeof t.document<"u"&&typeof t.document.createElement<"u";J(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let a=e.hydrationRouteProperties||[],n=e.mapRouteProperties||oo,o={},l=Mt(e.routes,n,void 0,o),s,i=e.basename||"/",u=e.dataStrategy||co,c={unstable_middleware:!1,...e.future},p=null,f=new Set,g=null,w=null,b=null,S=e.hydrationData!=null,R=Ce(l,e.history.location,i),O=!1,T=null,M;if(R==null&&!e.patchRoutesOnNavigation){let d=be(404,{pathname:e.history.location.pathname}),{matches:v,route:E}=an(l);M=!0,R=v,T={[E.id]:d}}else if(R&&!e.hydrationData&&pt(R,l,e.history.location.pathname).active&&(R=null),R)if(R.some(d=>d.route.lazy))M=!1;else if(!R.some(d=>d.route.loader))M=!0;else{let d=e.hydrationData?e.hydrationData.loaderData:null,v=e.hydrationData?e.hydrationData.errors:null;if(v){let E=R.findIndex(C=>v[C.route.id]!==void 0);M=R.slice(0,E+1).every(C=>!rr(C.route,d,v))}else M=R.every(E=>!rr(E.route,d,v))}else{M=!1,R=[];let d=pt(null,l,e.history.location.pathname);d.active&&d.matches&&(O=!0,R=d.matches)}let x,y={historyAction:e.history.action,location:e.history.location,matches:R,initialized:M,navigation:Xt,restoreScrollPosition:e.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||T,fetchers:new Map,blockers:new Map},D="POP",U=!1,j,K=!1,ne=new Map,se=null,oe=!1,X=!1,Q=new Set,G=new Map,ue=0,de=-1,ce=new Map,m=new Set,L=new Map,N=new Map,k=new Set,Y=new Map,ee,z=null;function Le(){if(p=e.history.listen(({action:d,location:v,delta:E})=>{if(ee){ee(),ee=void 0;return}re(Y.size===0||E!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let C=Nr({currentLocation:y.location,nextLocation:v,historyAction:d});if(C&&E!=null){let P=new Promise(A=>{ee=A});e.history.go(E*-1),mt(C,{state:"blocked",location:v,proceed(){mt(C,{state:"proceeding",proceed:void 0,reset:void 0,location:v}),P.then(()=>e.history.go(E))},reset(){let A=new Map(y.blockers);A.set(C,at),he({blockers:A})}});return}return Ie(d,v)}),r){bo(t,ne);let d=()=>So(t,ne);t.addEventListener("pagehide",d),se=()=>t.removeEventListener("pagehide",d)}return y.initialized||Ie("POP",y.location,{initialHydration:!0}),x}function ae(){p&&p(),se&&se(),f.clear(),j&&j.abort(),y.fetchers.forEach((d,v)=>Ut(v)),y.blockers.forEach((d,v)=>kr(v))}function jt(d){return f.add(d),()=>f.delete(d)}function he(d,v={}){y={...y,...d};let E=[],C=[];y.fetchers.forEach((P,A)=>{P.state==="idle"&&(k.has(A)?E.push(A):C.push(A))}),k.forEach(P=>{!y.fetchers.has(P)&&!G.has(P)&&E.push(P)}),[...f].forEach(P=>P(y,{deletedFetchers:E,viewTransitionOpts:v.viewTransitionOpts,flushSync:v.flushSync===!0})),E.forEach(P=>Ut(P)),C.forEach(P=>y.fetchers.delete(P))}function We(d,v,{flushSync:E}={}){var F,B;let C=y.actionData!=null&&y.navigation.formMethod!=null&&ve(y.navigation.formMethod)&&y.navigation.state==="loading"&&((F=d.state)==null?void 0:F._isRedirect)!==!0,P;v.actionData?Object.keys(v.actionData).length>0?P=v.actionData:P=null:C?P=y.actionData:P=null;let A=v.loaderData?rn(y.loaderData,v.loaderData,v.matches||[],v.errors):y.loaderData,H=y.blockers;H.size>0&&(H=new Map(H),H.forEach(($,V)=>H.set(V,at)));let _=U===!0||y.navigation.formMethod!=null&&ve(y.navigation.formMethod)&&((B=d.state)==null?void 0:B._isRedirect)!==!0;s&&(l=s,s=void 0),oe||D==="POP"||(D==="PUSH"?e.history.push(d,d.state):D==="REPLACE"&&e.history.replace(d,d.state));let I;if(D==="POP"){let $=ne.get(y.location.pathname);$&&$.has(d.pathname)?I={currentLocation:y.location,nextLocation:d}:ne.has(d.pathname)&&(I={currentLocation:d,nextLocation:y.location})}else if(K){let $=ne.get(y.location.pathname);$?$.add(d.pathname):($=new Set([d.pathname]),ne.set(y.location.pathname,$)),I={currentLocation:y.location,nextLocation:d}}he({...v,actionData:P,loaderData:A,historyAction:D,location:d,initialized:!0,navigation:Xt,revalidation:"idle",restoreScrollPosition:Ir(d,v.matches||y.matches),preventScrollReset:_,blockers:H},{viewTransitionOpts:I,flushSync:E===!0}),D="POP",U=!1,K=!1,oe=!1,X=!1,z==null||z.resolve(),z=null}async function Cr(d,v){if(typeof d=="number"){e.history.go(d);return}let E=tr(y.location,y.matches,i,d,v==null?void 0:v.fromRouteId,v==null?void 0:v.relative),{path:C,submission:P,error:A}=Jr(!1,E,v),H=y.location,_=ut(y.location,C,v&&v.state);_={..._,...e.history.encodeLocation(_)};let I=v&&v.replace!=null?v.replace:void 0,F="PUSH";I===!0?F="REPLACE":I===!1||P!=null&&ve(P.formMethod)&&P.formAction===y.location.pathname+y.location.search&&(F="REPLACE");let B=v&&"preventScrollReset"in v?v.preventScrollReset===!0:void 0,$=(v&&v.flushSync)===!0,V=Nr({currentLocation:H,nextLocation:_,historyAction:F});if(V){mt(V,{state:"blocked",location:_,proceed(){mt(V,{state:"proceeding",proceed:void 0,reset:void 0,location:_}),Cr(d,v)},reset(){let te=new Map(y.blockers);te.set(V,at),he({blockers:te})}});return}await Ie(F,_,{submission:P,pendingError:A,preventScrollReset:B,replace:v&&v.replace,enableViewTransition:v&&v.viewTransition,flushSync:$})}function ia(){z||(z=xo()),Ht(),he({revalidation:"loading"});let d=z.promise;return y.navigation.state==="submitting"?d:y.navigation.state==="idle"?(Ie(y.historyAction,y.location,{startUninterruptedRevalidation:!0}),d):(Ie(D||y.historyAction,y.navigation.location,{overrideNavigation:y.navigation,enableViewTransition:K===!0}),d)}async function Ie(d,v,E){j&&j.abort(),j=null,D=d,oe=(E&&E.startUninterruptedRevalidation)===!0,ya(y.location,y.matches),U=(E&&E.preventScrollReset)===!0,K=(E&&E.enableViewTransition)===!0;let C=s||l,P=E&&E.overrideNavigation,A=E!=null&&E.initialHydration&&y.matches&&y.matches.length>0&&!O?y.matches:Ce(C,v,i),H=(E&&E.flushSync)===!0;if(A&&y.initialized&&!X&&vo(y.location,v)&&!(E&&E.submission&&ve(E.submission.formMethod))){We(v,{matches:A},{flushSync:H});return}let _=pt(A,C,v.pathname);if(_.active&&_.matches&&(A=_.matches),!A){let{error:fe,notFoundMatches:pe,route:q}=zt(v.pathname);We(v,{matches:pe,loaderData:{},errors:{[q.id]:fe}},{flushSync:H});return}j=new AbortController;let I=Ve(e.history,v,j.signal,E&&E.submission),F=new Yr(e.unstable_getContext?await e.unstable_getContext():void 0),B;if(E&&E.pendingError)B=[He(A).route.id,{type:"error",error:E.pendingError}];else if(E&&E.submission&&ve(E.submission.formMethod)){let fe=await la(I,v,E.submission,A,F,_.active,E&&E.initialHydration===!0,{replace:E.replace,flushSync:H});if(fe.shortCircuited)return;if(fe.pendingActionResult){let[pe,q]=fe.pendingActionResult;if(ye(q)&&ze(q.error)&&q.error.status===404){j=null,We(v,{matches:fe.matches,loaderData:{},errors:{[pe]:q.error}});return}}A=fe.matches||A,B=fe.pendingActionResult,P=qt(v,E.submission),H=!1,_.active=!1,I=Ve(e.history,I.url,I.signal)}let{shortCircuited:$,matches:V,loaderData:te,errors:le}=await sa(I,v,A,F,_.active,P,E&&E.submission,E&&E.fetcherSubmission,E&&E.replace,E&&E.initialHydration===!0,H,B);$||(j=null,We(v,{matches:V||A,...nn(B),loaderData:te,errors:le}))}async function la(d,v,E,C,P,A,H,_={}){Ht();let I=Eo(v,E);if(he({navigation:I},{flushSync:_.flushSync===!0}),A){let $=await yt(C,v.pathname,d.signal);if($.type==="aborted")return{shortCircuited:!0};if($.type==="error"){let V=He($.partialMatches).route.id;return{matches:$.partialMatches,pendingActionResult:[V,{type:"error",error:$.error}]}}else if($.matches)C=$.matches;else{let{notFoundMatches:V,error:te,route:le}=zt(v.pathname);return{matches:V,pendingActionResult:[le.id,{type:"error",error:te}]}}}let F,B=st(C,v);if(!B.route.action&&!B.route.lazy)F={type:"error",error:be(405,{method:d.method,pathname:v.pathname,routeId:B.route.id})};else{let $=Je(n,o,d,C,B,H?[]:a,P),V=await Ke(d,$,P,null);if(F=V[B.route.id],!F){for(let te of C)if(V[te.route.id]){F=V[te.route.id];break}}if(d.signal.aborted)return{shortCircuited:!0}}if(Ue(F)){let $;return _&&_.replace!=null?$=_.replace:$=Zr(F.response.headers.get("Location"),new URL(d.url),i)===y.location.pathname+y.location.search,await $e(d,F,!0,{submission:E,replace:$}),{shortCircuited:!0}}if(ye(F)){let $=He(C,B.route.id);return(_&&_.replace)!==!0&&(D="PUSH"),{matches:C,pendingActionResult:[$.route.id,F,B.route.id]}}return{matches:C,pendingActionResult:[B.route.id,F]}}async function sa(d,v,E,C,P,A,H,_,I,F,B,$){let V=A||qt(v,H),te=H||_||on(V),le=!oe&&!F;if(P){if(le){let Re=Pr($);he({navigation:V,...Re!==void 0?{actionData:Re}:{}},{flushSync:B})}let Z=await yt(E,v.pathname,d.signal);if(Z.type==="aborted")return{shortCircuited:!0};if(Z.type==="error"){let Re=He(Z.partialMatches).route.id;return{matches:Z.partialMatches,loaderData:{},errors:{[Re]:Z.error}}}else if(Z.matches)E=Z.matches;else{let{error:Re,notFoundMatches:De,route:gt}=zt(v.pathname);return{matches:De,loaderData:{},errors:{[gt.id]:Re}}}}let fe=s||l,{dsMatches:pe,revalidatingFetchers:q}=Gr(d,C,n,o,e.history,y,E,te,v,F?[]:a,F===!0,X,Q,k,L,m,fe,i,e.patchRoutesOnNavigation!=null,$);if(de=++ue,!e.dataStrategy&&!pe.some(Z=>Z.shouldLoad)&&q.length===0){let Z=Dr();return We(v,{matches:E,loaderData:{},errors:$&&ye($[1])?{[$[0]]:$[1].error}:null,...nn($),...Z?{fetchers:new Map(y.fetchers)}:{}},{flushSync:B}),{shortCircuited:!0}}if(le){let Z={};if(!P){Z.navigation=V;let Re=Pr($);Re!==void 0&&(Z.actionData=Re)}q.length>0&&(Z.fetchers=ua(q)),he(Z,{flushSync:B})}q.forEach(Z=>{_e(Z.key),Z.controller&&G.set(Z.key,Z.controller)});let Qe=()=>q.forEach(Z=>_e(Z.key));j&&j.signal.addEventListener("abort",Qe);let{loaderResults:Fe,fetcherResults:Ze}=await Tr(pe,q,d,C);if(d.signal.aborted)return{shortCircuited:!0};j&&j.signal.removeEventListener("abort",Qe),q.forEach(Z=>G.delete(Z.key));let Ee=wt(Fe);if(Ee)return await $e(d,Ee.result,!0,{replace:I}),{shortCircuited:!0};if(Ee=wt(Ze),Ee)return m.add(Ee.key),await $e(d,Ee.result,!0,{replace:I}),{shortCircuited:!0};let{loaderData:et,errors:tt}=tn(y,E,Fe,$,q,Ze);F&&y.errors&&(tt={...y.errors,...tt});let Bt=Dr(),je=Or(de),vt=Bt||je||q.length>0;return{matches:E,loaderData:et,errors:tt,...vt?{fetchers:new Map(y.fetchers)}:{}}}function Pr(d){if(d&&!ye(d[1]))return{[d[0]]:d[1].data};if(y.actionData)return Object.keys(y.actionData).length===0?null:y.actionData}function ua(d){return d.forEach(v=>{let E=y.fetchers.get(v.key),C=ot(void 0,E?E.data:void 0);y.fetchers.set(v.key,C)}),new Map(y.fetchers)}async function ca(d,v,E,C){_e(d);let P=(C&&C.flushSync)===!0,A=s||l,H=tr(y.location,y.matches,i,E,v,C==null?void 0:C.relative),_=Ce(A,H,i),I=pt(_,A,H);if(I.active&&I.matches&&(_=I.matches),!_){Te(d,v,be(404,{pathname:H}),{flushSync:P});return}let{path:F,submission:B,error:$}=Jr(!0,H,C);if($){Te(d,v,$,{flushSync:P});return}let V=st(_,F),te=new Yr(e.unstable_getContext?await e.unstable_getContext():void 0),le=(C&&C.preventScrollReset)===!0;if(B&&ve(B.formMethod)){await da(d,v,F,V,_,te,I.active,P,le,B);return}L.set(d,{routeId:v,path:F}),await fa(d,v,F,V,_,te,I.active,P,le,B)}async function da(d,v,E,C,P,A,H,_,I,F){Ht(),L.delete(d);function B(ie){if(!ie.route.action&&!ie.route.lazy){let Ye=be(405,{method:F.formMethod,pathname:E,routeId:v});return Te(d,v,Ye,{flushSync:_}),!0}return!1}if(!H&&B(C))return;let $=y.fetchers.get(d);Me(d,Ro(F,$),{flushSync:_});let V=new AbortController,te=Ve(e.history,E,V.signal,F);if(H){let ie=await yt(P,E,te.signal,d);if(ie.type==="aborted")return;if(ie.type==="error"){Te(d,v,ie.error,{flushSync:_});return}else if(ie.matches){if(P=ie.matches,C=st(P,E),B(C))return}else{Te(d,v,be(404,{pathname:E}),{flushSync:_});return}}G.set(d,V);let le=ue,fe=Je(n,o,te,P,C,a,A),q=(await Ke(te,fe,A,d))[C.route.id];if(te.signal.aborted){G.get(d)===V&&G.delete(d);return}if(k.has(d)){if(Ue(q)||ye(q)){Me(d,Oe(void 0));return}}else{if(Ue(q))if(G.delete(d),de>le){Me(d,Oe(void 0));return}else return m.add(d),Me(d,ot(F)),$e(te,q,!1,{fetcherSubmission:F,preventScrollReset:I});if(ye(q)){Te(d,v,q.error);return}}let Qe=y.navigation.location||y.location,Fe=Ve(e.history,Qe,V.signal),Ze=s||l,Ee=y.navigation.state!=="idle"?Ce(Ze,y.navigation.location,i):y.matches;J(Ee,"Didn't find any matches after fetcher action");let et=++ue;ce.set(d,et);let tt=ot(F,q.data);y.fetchers.set(d,tt);let{dsMatches:Bt,revalidatingFetchers:je}=Gr(Fe,A,n,o,e.history,y,Ee,F,Qe,a,!1,X,Q,k,L,m,Ze,i,e.patchRoutesOnNavigation!=null,[C.route.id,q]);je.filter(ie=>ie.key!==d).forEach(ie=>{let Ye=ie.key,$r=y.fetchers.get(Ye),wa=ot(void 0,$r?$r.data:void 0);y.fetchers.set(Ye,wa),_e(Ye),ie.controller&&G.set(Ye,ie.controller)}),he({fetchers:new Map(y.fetchers)});let vt=()=>je.forEach(ie=>_e(ie.key));V.signal.addEventListener("abort",vt);let{loaderResults:Z,fetcherResults:Re}=await Tr(Bt,je,Fe,A);if(V.signal.aborted)return;if(V.signal.removeEventListener("abort",vt),ce.delete(d),G.delete(d),je.forEach(ie=>G.delete(ie.key)),y.fetchers.has(d)){let ie=Oe(q.data);y.fetchers.set(d,ie)}let De=wt(Z);if(De)return $e(Fe,De.result,!1,{preventScrollReset:I});if(De=wt(Re),De)return m.add(De.key),$e(Fe,De.result,!1,{preventScrollReset:I});let{loaderData:gt,errors:Wt}=tn(y,Ee,Z,void 0,je,Re);Or(et),y.navigation.state==="loading"&&et>de?(J(D,"Expected pending action"),j&&j.abort(),We(y.navigation.location,{matches:Ee,loaderData:gt,errors:Wt,fetchers:new Map(y.fetchers)})):(he({errors:Wt,loaderData:rn(y.loaderData,gt,Ee,Wt),fetchers:new Map(y.fetchers)}),X=!1)}async function fa(d,v,E,C,P,A,H,_,I,F){let B=y.fetchers.get(d);Me(d,ot(F,B?B.data:void 0),{flushSync:_});let $=new AbortController,V=Ve(e.history,E,$.signal);if(H){let q=await yt(P,E,V.signal,d);if(q.type==="aborted")return;if(q.type==="error"){Te(d,v,q.error,{flushSync:_});return}else if(q.matches)P=q.matches,C=st(P,E);else{Te(d,v,be(404,{pathname:E}),{flushSync:_});return}}G.set(d,$);let te=ue,le=Je(n,o,V,P,C,a,A),pe=(await Ke(V,le,A,d))[C.route.id];if(G.get(d)===$&&G.delete(d),!V.signal.aborted){if(k.has(d)){Me(d,Oe(void 0));return}if(Ue(pe))if(de>te){Me(d,Oe(void 0));return}else{m.add(d),await $e(V,pe,!1,{preventScrollReset:I});return}if(ye(pe)){Te(d,v,pe.error);return}Me(d,Oe(pe.data))}}async function $e(d,v,E,{submission:C,fetcherSubmission:P,preventScrollReset:A,replace:H}={}){v.response.headers.has("X-Remix-Revalidate")&&(X=!0);let _=v.response.headers.get("Location");J(_,"Expected a Location header on the redirect Response"),_=Zr(_,new URL(d.url),i);let I=ut(y.location,_,{_isRedirect:!0});if(r){let le=!1;if(v.response.headers.has("X-Remix-Reload-Document"))le=!0;else if(dr.test(_)){const fe=yn(_,!0);le=fe.origin!==t.location.origin||ge(fe.pathname,i)==null}if(le){H?t.location.replace(_):t.location.assign(_);return}}j=null;let F=H===!0||v.response.headers.has("X-Remix-Replace")?"REPLACE":"PUSH",{formMethod:B,formAction:$,formEncType:V}=y.navigation;!C&&!P&&B&&$&&V&&(C=on(y.navigation));let te=C||P;if(no.has(v.response.status)&&te&&ve(te.formMethod))await Ie(F,I,{submission:{...te,formAction:_},preventScrollReset:A||U,enableViewTransition:E?K:void 0});else{let le=qt(I,C);await Ie(F,I,{overrideNavigation:le,fetcherSubmission:P,preventScrollReset:A||U,enableViewTransition:E?K:void 0})}}async function Ke(d,v,E,C){let P,A={};try{P=await fo(u,d,v,C,E,!1)}catch(H){return v.filter(_=>_.shouldLoad).forEach(_=>{A[_.route.id]={type:"error",error:H}}),A}if(d.signal.aborted)return A;for(let[H,_]of Object.entries(P))if(go(_)){let I=_.result;A[H]={type:"redirect",response:po(I,d,H,v,i)}}else A[H]=await mo(_);return A}async function Tr(d,v,E,C){let P=Ke(E,d,C,null),A=Promise.all(v.map(async I=>{if(I.matches&&I.match&&I.request&&I.controller){let B=(await Ke(I.request,I.matches,C,I.key))[I.match.route.id];return{[I.key]:B}}else return Promise.resolve({[I.key]:{type:"error",error:be(404,{pathname:I.path})}})})),H=await P,_=(await A).reduce((I,F)=>Object.assign(I,F),{});return{loaderResults:H,fetcherResults:_}}function Ht(){X=!0,L.forEach((d,v)=>{G.has(v)&&Q.add(v),_e(v)})}function Me(d,v,E={}){y.fetchers.set(d,v),he({fetchers:new Map(y.fetchers)},{flushSync:(E&&E.flushSync)===!0})}function Te(d,v,E,C={}){let P=He(y.matches,v);Ut(d),he({errors:{[P.route.id]:E},fetchers:new Map(y.fetchers)},{flushSync:(C&&C.flushSync)===!0})}function Mr(d){return N.set(d,(N.get(d)||0)+1),k.has(d)&&k.delete(d),y.fetchers.get(d)||ao}function Ut(d){let v=y.fetchers.get(d);G.has(d)&&!(v&&v.state==="loading"&&ce.has(d))&&_e(d),L.delete(d),ce.delete(d),m.delete(d),k.delete(d),Q.delete(d),y.fetchers.delete(d)}function ha(d){let v=(N.get(d)||0)-1;v<=0?(N.delete(d),k.add(d)):N.set(d,v),he({fetchers:new Map(y.fetchers)})}function _e(d){let v=G.get(d);v&&(v.abort(),G.delete(d))}function _r(d){for(let v of d){let E=Mr(v),C=Oe(E.data);y.fetchers.set(v,C)}}function Dr(){let d=[],v=!1;for(let E of m){let C=y.fetchers.get(E);J(C,`Expected fetcher: ${E}`),C.state==="loading"&&(m.delete(E),d.push(E),v=!0)}return _r(d),v}function Or(d){let v=[];for(let[E,C]of ce)if(C<d){let P=y.fetchers.get(E);J(P,`Expected fetcher: ${E}`),P.state==="loading"&&(_e(E),ce.delete(E),v.push(E))}return _r(v),v.length>0}function ma(d,v){let E=y.blockers.get(d)||at;return Y.get(d)!==v&&Y.set(d,v),E}function kr(d){y.blockers.delete(d),Y.delete(d)}function mt(d,v){let E=y.blockers.get(d)||at;J(E.state==="unblocked"&&v.state==="blocked"||E.state==="blocked"&&v.state==="blocked"||E.state==="blocked"&&v.state==="proceeding"||E.state==="blocked"&&v.state==="unblocked"||E.state==="proceeding"&&v.state==="unblocked",`Invalid blocker state transition: ${E.state} -> ${v.state}`);let C=new Map(y.blockers);C.set(d,v),he({blockers:C})}function Nr({currentLocation:d,nextLocation:v,historyAction:E}){if(Y.size===0)return;Y.size>1&&re(!1,"A router only supports one blocker at a time");let C=Array.from(Y.entries()),[P,A]=C[C.length-1],H=y.blockers.get(P);if(!(H&&H.state==="proceeding")&&A({currentLocation:d,nextLocation:v,historyAction:E}))return P}function zt(d){let v=be(404,{pathname:d}),E=s||l,{matches:C,route:P}=an(E);return{notFoundMatches:C,route:P,error:v}}function pa(d,v,E){if(g=d,b=v,w=E||null,!S&&y.navigation===Xt){S=!0;let C=Ir(y.location,y.matches);C!=null&&he({restoreScrollPosition:C})}return()=>{g=null,b=null,w=null}}function Ar(d,v){return w&&w(d,v.map(C=>vn(C,y.loaderData)))||d.key}function ya(d,v){if(g&&b){let E=Ar(d,v);g[E]=b()}}function Ir(d,v){if(g){let E=Ar(d,v),C=g[E];if(typeof C=="number")return C}return null}function pt(d,v,E){if(e.patchRoutesOnNavigation)if(d){if(Object.keys(d[0].params).length>0)return{active:!0,matches:xt(v,E,i,!0)}}else return{active:!0,matches:xt(v,E,i,!0)||[]};return{active:!1,matches:null}}async function yt(d,v,E,C){if(!e.patchRoutesOnNavigation)return{type:"success",matches:d};let P=d;for(;;){let A=s==null,H=s||l,_=o;try{await e.patchRoutesOnNavigation({signal:E,path:v,matches:P,fetcherKey:C,patch:(B,$)=>{E.aborted||Xr(B,$,H,_,n)}})}catch(B){return{type:"error",error:B,partialMatches:P}}finally{A&&!E.aborted&&(l=[...l])}if(E.aborted)return{type:"aborted"};let I=Ce(H,v,i);if(I)return{type:"success",matches:I};let F=xt(H,v,i,!0);if(!F||P.length===F.length&&P.every((B,$)=>B.route.id===F[$].route.id))return{type:"success",matches:null};P=F}}function va(d){o={},s=Mt(d,n,void 0,o)}function ga(d,v){let E=s==null;Xr(d,v,s||l,o,n),E&&(l=[...l],he({}))}return x={get basename(){return i},get future(){return c},get state(){return y},get routes(){return l},get window(){return t},initialize:Le,subscribe:jt,enableScrollRestoration:pa,navigate:Cr,fetch:ca,revalidate:ia,createHref:d=>e.history.createHref(d),encodeLocation:d=>e.history.encodeLocation(d),getFetcher:Mr,deleteFetcher:ha,dispose:ae,getBlocker:ma,deleteBlocker:kr,patchRoutes:ga,_internalFetchControllers:G,_internalSetRoutes:va},x}function io(e){return e!=null&&("formData"in e&&e.formData!=null||"body"in e&&e.body!==void 0)}function tr(e,t,r,a,n,o){let l,s;if(n){l=[];for(let u of t)if(l.push(u),u.route.id===n){s=u;break}}else l=t,s=t[t.length-1];let i=Nt(a||".",kt(l),ge(e.pathname,r)||e.pathname,o==="path");if(a==null&&(i.search=e.search,i.hash=e.hash),(a==null||a===""||a===".")&&s){let u=hr(i.search);if(s.route.index&&!u)i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index";else if(!s.route.index&&u){let c=new URLSearchParams(i.search),p=c.getAll("index");c.delete("index"),p.filter(g=>g).forEach(g=>c.append("index",g));let f=c.toString();i.search=f?`?${f}`:""}}return r!=="/"&&(i.pathname=i.pathname==="/"?r:Pe([r,i.pathname])),ke(i)}function Jr(e,t,r){if(!r||!io(r))return{path:t};if(r.formMethod&&!wo(r.formMethod))return{path:t,error:be(405,{method:r.formMethod})};let a=()=>({path:t,error:be(400,{type:"invalid-body"})}),o=(r.formMethod||"get").toUpperCase(),l=_n(t);if(r.body!==void 0){if(r.formEncType==="text/plain"){if(!ve(o))return a();let p=typeof r.body=="string"?r.body:r.body instanceof FormData||r.body instanceof URLSearchParams?Array.from(r.body.entries()).reduce((f,[g,w])=>`${f}${g}=${w}
`,""):String(r.body);return{path:t,submission:{formMethod:o,formAction:l,formEncType:r.formEncType,formData:void 0,json:void 0,text:p}}}else if(r.formEncType==="application/json"){if(!ve(o))return a();try{let p=typeof r.body=="string"?JSON.parse(r.body):r.body;return{path:t,submission:{formMethod:o,formAction:l,formEncType:r.formEncType,formData:void 0,json:p,text:void 0}}}catch{return a()}}}J(typeof FormData=="function","FormData is not available in this environment");let s,i;if(r.formData)s=ar(r.formData),i=r.formData;else if(r.body instanceof FormData)s=ar(r.body),i=r.body;else if(r.body instanceof URLSearchParams)s=r.body,i=en(s);else if(r.body==null)s=new URLSearchParams,i=new FormData;else try{s=new URLSearchParams(r.body),i=en(s)}catch{return a()}let u={formMethod:o,formAction:l,formEncType:r&&r.formEncType||"application/x-www-form-urlencoded",formData:i,json:void 0,text:void 0};if(ve(u.formMethod))return{path:t,submission:u};let c=Ae(t);return e&&c.search&&hr(c.search)&&s.append("index",""),c.search=`?${s}`,{path:ke(c),submission:u}}function Gr(e,t,r,a,n,o,l,s,i,u,c,p,f,g,w,b,S,R,O,T){var oe;let M=T?ye(T[1])?T[1].error:T[1].data:void 0,x=n.createURL(o.location),y=n.createURL(i),D;if(c&&o.errors){let X=Object.keys(o.errors)[0];D=l.findIndex(Q=>Q.route.id===X)}else if(T&&ye(T[1])){let X=T[0];D=l.findIndex(Q=>Q.route.id===X)-1}let U=T?T[1].statusCode:void 0,j=U&&U>=400,K={currentUrl:x,currentParams:((oe=o.matches[0])==null?void 0:oe.params)||{},nextUrl:y,nextParams:l[0].params,...s,actionResult:M,actionStatus:U},ne=l.map((X,Q)=>{let{route:G}=X,ue=null;if(D!=null&&Q>D?ue=!1:G.lazy?ue=!0:G.loader==null?ue=!1:c?ue=rr(G,o.loaderData,o.errors):lo(o.loaderData,o.matches[Q],X)&&(ue=!0),ue!==null)return nr(r,a,e,X,u,t,ue);let de=j?!1:p||x.pathname+x.search===y.pathname+y.search||x.search!==y.search||so(o.matches[Q],X),ce={...K,defaultShouldRevalidate:de},m=Dt(X,ce);return nr(r,a,e,X,u,t,m,ce)}),se=[];return w.forEach((X,Q)=>{if(c||!l.some(k=>k.route.id===X.routeId)||g.has(Q))return;let G=o.fetchers.get(Q),ue=G&&G.state!=="idle"&&G.data===void 0,de=Ce(S,X.path,R);if(!de){if(O&&ue)return;se.push({key:Q,routeId:X.routeId,path:X.path,matches:null,match:null,request:null,controller:null});return}if(b.has(Q))return;let ce=st(de,X.path),m=new AbortController,L=Ve(n,X.path,m.signal),N=null;if(f.has(Q))f.delete(Q),N=Je(r,a,L,de,ce,u,t);else if(ue)p&&(N=Je(r,a,L,de,ce,u,t));else{let k={...K,defaultShouldRevalidate:j?!1:p};Dt(ce,k)&&(N=Je(r,a,L,de,ce,u,t,k))}N&&se.push({key:Q,routeId:X.routeId,path:X.path,matches:N,match:ce,request:L,controller:m})}),{dsMatches:ne,revalidatingFetchers:se}}function rr(e,t,r){if(e.lazy)return!0;if(!e.loader)return!1;let a=t!=null&&e.id in t,n=r!=null&&r[e.id]!==void 0;return!a&&n?!1:typeof e.loader=="function"&&e.loader.hydrate===!0?!0:!a&&!n}function lo(e,t,r){let a=!t||r.route.id!==t.route.id,n=!e.hasOwnProperty(r.route.id);return a||n}function so(e,t){let r=e.route.path;return e.pathname!==t.pathname||r!=null&&r.endsWith("*")&&e.params["*"]!==t.params["*"]}function Dt(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if(typeof r=="boolean")return r}return t.defaultShouldRevalidate}function Xr(e,t,r,a,n){let o;if(e){let i=a[e];J(i,`No route found to patch children into: routeId = ${e}`),i.children||(i.children=[]),o=i.children}else o=r;let l=t.filter(i=>!o.some(u=>Ln(i,u))),s=Mt(l,n,[e||"_","patch",String((o==null?void 0:o.length)||"0")],a);o.push(...s)}function Ln(e,t){return"id"in e&&"id"in t&&e.id===t.id?!0:e.index===t.index&&e.path===t.path&&e.caseSensitive===t.caseSensitive?(!e.children||e.children.length===0)&&(!t.children||t.children.length===0)?!0:e.children.every((r,a)=>{var n;return(n=t.children)==null?void 0:n.some(o=>Ln(r,o))}):!1}var qr=new WeakMap,Cn=({key:e,route:t,manifest:r,mapRouteProperties:a})=>{let n=r[t.id];if(J(n,"No route found in manifest"),!n.lazy||typeof n.lazy!="object")return;let o=n.lazy[e];if(!o)return;let l=qr.get(n);l||(l={},qr.set(n,l));let s=l[e];if(s)return s;let i=(async()=>{let u=_a(e),p=n[e]!==void 0&&e!=="hasErrorBoundary";if(u)re(!u,"Route property "+e+" is not a supported lazy route property. This property will be ignored."),l[e]=Promise.resolve();else if(p)re(!1,`Route "${n.id}" has a static property "${e}" defined. The lazy property will be ignored.`);else{let f=await o();f!=null&&(Object.assign(n,{[e]:f}),Object.assign(n,a(n)))}typeof n.lazy=="object"&&(n.lazy[e]=void 0,Object.values(n.lazy).every(f=>f===void 0)&&(n.lazy=void 0))})();return l[e]=i,i},Kr=new WeakMap;function uo(e,t,r,a,n){let o=r[e.id];if(J(o,"No route found in manifest"),!e.lazy)return{lazyRoutePromise:void 0,lazyHandlerPromise:void 0};if(typeof e.lazy=="function"){let c=Kr.get(o);if(c)return{lazyRoutePromise:c,lazyHandlerPromise:c};let p=(async()=>{J(typeof e.lazy=="function","No lazy route function found");let f=await e.lazy(),g={};for(let w in f){let b=f[w];if(b===void 0)continue;let S=Oa(w),O=o[w]!==void 0&&w!=="hasErrorBoundary";S?re(!S,"Route property "+w+" is not a supported property to be returned from a lazy route function. This property will be ignored."):O?re(!O,`Route "${o.id}" has a static property "${w}" defined but its lazy function is also returning a value for this property. The lazy route property "${w}" will be ignored.`):g[w]=b}Object.assign(o,g),Object.assign(o,{...a(o),lazy:void 0})})();return Kr.set(o,p),p.catch(()=>{}),{lazyRoutePromise:p,lazyHandlerPromise:p}}let l=Object.keys(e.lazy),s=[],i;for(let c of l){if(n&&n.includes(c))continue;let p=Cn({key:c,route:e,manifest:r,mapRouteProperties:a});p&&(s.push(p),c===t&&(i=p))}let u=s.length>0?Promise.all(s).then(()=>{}):void 0;return u==null||u.catch(()=>{}),i==null||i.catch(()=>{}),{lazyRoutePromise:u,lazyHandlerPromise:i}}async function Qr(e){let t=e.matches.filter(n=>n.shouldLoad),r={};return(await Promise.all(t.map(n=>n.resolve()))).forEach((n,o)=>{r[t[o].route.id]=n}),r}async function co(e){return e.matches.some(t=>t.route.unstable_middleware)?Pn(e,!1,()=>Qr(e),(t,r)=>({[r]:{type:"error",result:t}})):Qr(e)}async function Pn(e,t,r,a){let{matches:n,request:o,params:l,context:s}=e,i={handlerResult:void 0};try{let u=n.flatMap(p=>p.route.unstable_middleware?p.route.unstable_middleware.map(f=>[p.route.id,f]):[]),c=await Tn({request:o,params:l,context:s},u,t,i,r);return t?c:i.handlerResult}catch(u){if(!i.middlewareError)throw u;let c=await a(i.middlewareError.error,i.middlewareError.routeId);return i.handlerResult?Object.assign(i.handlerResult,c):c}}async function Tn(e,t,r,a,n,o=0){let{request:l}=e;if(l.signal.aborted)throw l.signal.reason?l.signal.reason:new Error(`Request aborted without an \`AbortSignal.reason\`: ${l.method} ${l.url}`);let s=t[o];if(!s)return a.handlerResult=await n(),a.handlerResult;let[i,u]=s,c=!1,p,f=async()=>{if(c)throw new Error("You may only call `next()` once per middleware");c=!0,await Tn(e,t,r,a,n,o+1)};try{let g=await u({request:e.request,params:e.params,context:e.context},f);return c?g===void 0?p:g:f()}catch(g){throw a.middlewareError?a.middlewareError.error!==g&&(a.middlewareError={routeId:i,error:g}):a.middlewareError={routeId:i,error:g},g}}function Mn(e,t,r,a,n){let o=Cn({key:"unstable_middleware",route:a.route,manifest:t,mapRouteProperties:e}),l=uo(a.route,ve(r.method)?"action":"loader",t,e,n);return{middleware:o,route:l.lazyRoutePromise,handler:l.lazyHandlerPromise}}function nr(e,t,r,a,n,o,l,s=null){let i=!1,u=Mn(e,t,r,a,n);return{...a,_lazyPromises:u,shouldLoad:l,unstable_shouldRevalidateArgs:s,unstable_shouldCallHandler(c){return i=!0,s?typeof c=="boolean"?Dt(a,{...s,defaultShouldRevalidate:c}):Dt(a,s):l},resolve(c){return i||l||c&&r.method==="GET"&&(a.route.lazy||a.route.loader)?ho({request:r,match:a,lazyHandlerPromise:u==null?void 0:u.handler,lazyRoutePromise:u==null?void 0:u.route,handlerOverride:c,scopedContext:o}):Promise.resolve({type:"data",result:void 0})}}}function Je(e,t,r,a,n,o,l,s=null){return a.map(i=>i.route.id!==n.route.id?{...i,shouldLoad:!1,unstable_shouldRevalidateArgs:s,unstable_shouldCallHandler:()=>!1,_lazyPromises:Mn(e,t,r,i,o),resolve:()=>Promise.resolve({type:"data",result:void 0})}:nr(e,t,r,i,o,l,!0,s))}async function fo(e,t,r,a,n,o){r.some(u=>{var c;return(c=u._lazyPromises)==null?void 0:c.middleware})&&await Promise.all(r.map(u=>{var c;return(c=u._lazyPromises)==null?void 0:c.middleware}));let l={request:t,params:r[0].params,context:n,matches:r},i=await e({...l,fetcherKey:a,unstable_runClientMiddleware:u=>{let c=l;return Pn(c,!1,()=>u({...c,fetcherKey:a,unstable_runClientMiddleware:()=>{throw new Error("Cannot call `unstable_runClientMiddleware()` from within an `unstable_runClientMiddleware` handler")}}),(p,f)=>({[f]:{type:"error",result:p}}))}});try{await Promise.all(r.flatMap(u=>{var c,p;return[(c=u._lazyPromises)==null?void 0:c.handler,(p=u._lazyPromises)==null?void 0:p.route]}))}catch{}return i}async function ho({request:e,match:t,lazyHandlerPromise:r,lazyRoutePromise:a,handlerOverride:n,scopedContext:o}){let l,s,i=ve(e.method),u=i?"action":"loader",c=p=>{let f,g=new Promise((S,R)=>f=R);s=()=>f(),e.signal.addEventListener("abort",s);let w=S=>typeof p!="function"?Promise.reject(new Error(`You cannot call the handler for a route which defines a boolean "${u}" [routeId: ${t.route.id}]`)):p({request:e,params:t.params,context:o},...S!==void 0?[S]:[]),b=(async()=>{try{return{type:"data",result:await(n?n(R=>w(R)):w())}}catch(S){return{type:"error",result:S}}})();return Promise.race([b,g])};try{let p=i?t.route.action:t.route.loader;if(r||a)if(p){let f,[g]=await Promise.all([c(p).catch(w=>{f=w}),r,a]);if(f!==void 0)throw f;l=g}else{await r;let f=i?t.route.action:t.route.loader;if(f)[l]=await Promise.all([c(f),a]);else if(u==="action"){let g=new URL(e.url),w=g.pathname+g.search;throw be(405,{method:e.method,pathname:w,routeId:t.route.id})}else return{type:"data",result:void 0}}else if(p)l=await c(p);else{let f=new URL(e.url),g=f.pathname+f.search;throw be(404,{pathname:g})}}catch(p){return{type:"error",result:p}}finally{s&&e.signal.removeEventListener("abort",s)}return l}async function mo(e){var a,n,o,l,s,i;let{result:t,type:r}=e;if(fr(t)){let u;try{let c=t.headers.get("Content-Type");c&&/\bapplication\/json\b/.test(c)?t.body==null?u=null:u=await t.json():u=await t.text()}catch(c){return{type:"error",error:c}}return r==="error"?{type:"error",error:new Ne(t.status,t.statusText,u),statusCode:t.status,headers:t.headers}:{type:"data",data:u,statusCode:t.status,headers:t.headers}}return r==="error"?or(t)?t.data instanceof Error?{type:"error",error:t.data,statusCode:(a=t.init)==null?void 0:a.status,headers:(n=t.init)!=null&&n.headers?new Headers(t.init.headers):void 0}:{type:"error",error:new Ne(((o=t.init)==null?void 0:o.status)||500,void 0,t.data),statusCode:ze(t)?t.status:void 0,headers:(l=t.init)!=null&&l.headers?new Headers(t.init.headers):void 0}:{type:"error",error:t,statusCode:ze(t)?t.status:void 0}:or(t)?{type:"data",data:t.data,statusCode:(s=t.init)==null?void 0:s.status,headers:(i=t.init)!=null&&i.headers?new Headers(t.init.headers):void 0}:{type:"data",data:t}}function po(e,t,r,a,n){let o=e.headers.get("Location");if(J(o,"Redirects returned/thrown from loaders/actions must have a Location header"),!dr.test(o)){let l=a.slice(0,a.findIndex(s=>s.route.id===r)+1);o=tr(new URL(t.url),l,n,o),e.headers.set("Location",o)}return e}function Zr(e,t,r){if(dr.test(e)){let a=e,n=a.startsWith("//")?new URL(t.protocol+a):new URL(a),o=ge(n.pathname,r)!=null;if(n.origin===t.origin&&o)return n.pathname+n.search+n.hash}return e}function Ve(e,t,r,a){let n=e.createURL(_n(t)).toString(),o={signal:r};if(a&&ve(a.formMethod)){let{formMethod:l,formEncType:s}=a;o.method=l.toUpperCase(),s==="application/json"?(o.headers=new Headers({"Content-Type":s}),o.body=JSON.stringify(a.json)):s==="text/plain"?o.body=a.text:s==="application/x-www-form-urlencoded"&&a.formData?o.body=ar(a.formData):o.body=a.formData}return new Request(n,o)}function ar(e){let t=new URLSearchParams;for(let[r,a]of e.entries())t.append(r,typeof a=="string"?a:a.name);return t}function en(e){let t=new FormData;for(let[r,a]of e.entries())t.append(r,a);return t}function yo(e,t,r,a=!1,n=!1){let o={},l=null,s,i=!1,u={},c=r&&ye(r[1])?r[1].error:void 0;return e.forEach(p=>{if(!(p.route.id in t))return;let f=p.route.id,g=t[f];if(J(!Ue(g),"Cannot handle redirect results in processLoaderData"),ye(g)){let w=g.error;if(c!==void 0&&(w=c,c=void 0),l=l||{},n)l[f]=w;else{let b=He(e,f);l[b.route.id]==null&&(l[b.route.id]=w)}a||(o[f]=xn),i||(i=!0,s=ze(g.error)?g.error.status:500),g.headers&&(u[f]=g.headers)}else o[f]=g.data,g.statusCode&&g.statusCode!==200&&!i&&(s=g.statusCode),g.headers&&(u[f]=g.headers)}),c!==void 0&&r&&(l={[r[0]]:c},r[2]&&(o[r[2]]=void 0)),{loaderData:o,errors:l,statusCode:s||200,loaderHeaders:u}}function tn(e,t,r,a,n,o){let{loaderData:l,errors:s}=yo(t,r,a);return n.filter(i=>!i.matches||i.matches.some(u=>u.shouldLoad)).forEach(i=>{let{key:u,match:c,controller:p}=i,f=o[u];if(J(f,"Did not find corresponding fetcher result"),!(p&&p.signal.aborted))if(ye(f)){let g=He(e.matches,c==null?void 0:c.route.id);s&&s[g.route.id]||(s={...s,[g.route.id]:f.error}),e.fetchers.delete(u)}else if(Ue(f))J(!1,"Unhandled fetcher revalidation redirect");else{let g=Oe(f.data);e.fetchers.set(u,g)}}),{loaderData:l,errors:s}}function rn(e,t,r,a){let n=Object.entries(t).filter(([,o])=>o!==xn).reduce((o,[l,s])=>(o[l]=s,o),{});for(let o of r){let l=o.route.id;if(!t.hasOwnProperty(l)&&e.hasOwnProperty(l)&&o.route.loader&&(n[l]=e[l]),a&&a.hasOwnProperty(l))break}return n}function nn(e){return e?ye(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function He(e,t){return(t?e.slice(0,e.findIndex(a=>a.route.id===t)+1):[...e]).reverse().find(a=>a.route.hasErrorBoundary===!0)||e[0]}function an(e){let t=e.length===1?e[0]:e.find(r=>r.index||!r.path||r.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function be(e,{pathname:t,routeId:r,method:a,type:n,message:o}={}){let l="Unknown Server Error",s="Unknown @remix-run/router error";return e===400?(l="Bad Request",a&&t&&r?s=`You made a ${a} request to "${t}" but did not provide a \`loader\` for route "${r}", so there is no way to handle the request.`:n==="invalid-body"&&(s="Unable to encode submission body")):e===403?(l="Forbidden",s=`Route "${r}" does not match URL "${t}"`):e===404?(l="Not Found",s=`No route matches URL "${t}"`):e===405&&(l="Method Not Allowed",a&&t&&r?s=`You made a ${a.toUpperCase()} request to "${t}" but did not provide an \`action\` for route "${r}", so there is no way to handle the request.`:a&&(s=`Invalid request method "${a.toUpperCase()}"`)),new Ne(e||500,l,new Error(s),!0)}function wt(e){let t=Object.entries(e);for(let r=t.length-1;r>=0;r--){let[a,n]=t[r];if(Ue(n))return{key:a,result:n}}}function _n(e){let t=typeof e=="string"?Ae(e):e;return ke({...t,hash:""})}function vo(e,t){return e.pathname!==t.pathname||e.search!==t.search?!1:e.hash===""?t.hash!=="":e.hash===t.hash?!0:t.hash!==""}function go(e){return fr(e.result)&&ro.has(e.result.status)}function ye(e){return e.type==="error"}function Ue(e){return(e&&e.type)==="redirect"}function or(e){return typeof e=="object"&&e!=null&&"type"in e&&"data"in e&&"init"in e&&e.type==="DataWithResponseInit"}function fr(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function wo(e){return to.has(e.toUpperCase())}function ve(e){return Za.has(e.toUpperCase())}function hr(e){return new URLSearchParams(e).getAll("index").some(t=>t==="")}function st(e,t){let r=typeof t=="string"?Ae(t).search:t.search;if(e[e.length-1].route.index&&hr(r||""))return e[e.length-1];let a=Rn(e);return a[a.length-1]}function on(e){let{formMethod:t,formAction:r,formEncType:a,text:n,formData:o,json:l}=e;if(!(!t||!r||!a)){if(n!=null)return{formMethod:t,formAction:r,formEncType:a,formData:void 0,json:void 0,text:n};if(o!=null)return{formMethod:t,formAction:r,formEncType:a,formData:o,json:void 0,text:void 0};if(l!==void 0)return{formMethod:t,formAction:r,formEncType:a,formData:void 0,json:l,text:void 0}}}function qt(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function Eo(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}function ot(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function Ro(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}function Oe(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}function bo(e,t){try{let r=e.sessionStorage.getItem(Sn);if(r){let a=JSON.parse(r);for(let[n,o]of Object.entries(a||{}))o&&Array.isArray(o)&&t.set(n,new Set(o||[]))}}catch{}}function So(e,t){if(t.size>0){let r={};for(let[a,n]of t)r[a]=[...n];try{e.sessionStorage.setItem(Sn,JSON.stringify(r))}catch(a){re(!1,`Failed to save applied view transitions in sessionStorage (${a}).`)}}}function xo(){let e,t,r=new Promise((a,n)=>{e=async o=>{a(o);try{await r}catch{}},t=async o=>{n(o);try{await r}catch{}}});return{promise:r,resolve:e,reject:t}}var Be=h.createContext(null);Be.displayName="DataRouter";var Ge=h.createContext(null);Ge.displayName="DataRouterState";var mr=h.createContext({isTransitioning:!1});mr.displayName="ViewTransition";var Dn=h.createContext(new Map);Dn.displayName="Fetchers";var Lo=h.createContext(null);Lo.displayName="Await";var we=h.createContext(null);we.displayName="Navigation";var At=h.createContext(null);At.displayName="Location";var Se=h.createContext({outlet:null,matches:[],isDataRoute:!1});Se.displayName="Route";var pr=h.createContext(null);pr.displayName="RouteError";function Co(e,{relative:t}={}){J(Xe(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:a}=h.useContext(we),{hash:n,pathname:o,search:l}=dt(e,{relative:t}),s=o;return r!=="/"&&(s=o==="/"?r:Pe([r,o])),a.createHref({pathname:s,search:l,hash:n})}function Xe(){return h.useContext(At)!=null}function xe(){return J(Xe(),"useLocation() may be used only in the context of a <Router> component."),h.useContext(At).location}var On="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function kn(e){h.useContext(we).static||h.useLayoutEffect(e)}function Nn(){let{isDataRoute:e}=h.useContext(Se);return e?Ho():Po()}function Po(){J(Xe(),"useNavigate() may be used only in the context of a <Router> component.");let e=h.useContext(Be),{basename:t,navigator:r}=h.useContext(we),{matches:a}=h.useContext(Se),{pathname:n}=xe(),o=JSON.stringify(kt(a)),l=h.useRef(!1);return kn(()=>{l.current=!0}),h.useCallback((i,u={})=>{if(re(l.current,On),!l.current)return;if(typeof i=="number"){r.go(i);return}let c=Nt(i,JSON.parse(o),n,u.relative==="path");e==null&&t!=="/"&&(c.pathname=c.pathname==="/"?t:Pe([t,c.pathname])),(u.replace?r.replace:r.push)(c,u.state,u)},[t,r,o,n,e])}var To=h.createContext(null);function Mo(e){let t=h.useContext(Se).outlet;return t&&h.createElement(To.Provider,{value:e},t)}function An(){let{matches:e}=h.useContext(Se),t=e[e.length-1];return t?t.params:{}}function dt(e,{relative:t}={}){let{matches:r}=h.useContext(Se),{pathname:a}=xe(),n=JSON.stringify(kt(r));return h.useMemo(()=>Nt(e,JSON.parse(n),a,t==="path"),[e,n,a,t])}function _o(e,t,r,a){J(Xe(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:n}=h.useContext(we),{matches:o}=h.useContext(Se),l=o[o.length-1],s=l?l.params:{},i=l?l.pathname:"/",u=l?l.pathnameBase:"/",c=l&&l.route;{let R=c&&c.path||"";Fn(i,!c||R.endsWith("*")||R.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${i}" (under <Route path="${R}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${R}"> to <Route path="${R==="/"?"*":`${R}/*`}">.`)}let p=xe(),f;f=p;let g=f.pathname||"/",w=g;if(u!=="/"){let R=u.replace(/^\//,"").split("/");w="/"+g.replace(/^\//,"").split("/").slice(R.length).join("/")}let b=Ce(e,{pathname:w});return re(c||b!=null,`No routes matched location "${f.pathname}${f.search}${f.hash}" `),re(b==null||b[b.length-1].route.element!==void 0||b[b.length-1].route.Component!==void 0||b[b.length-1].route.lazy!==void 0,`Matched leaf route at location "${f.pathname}${f.search}${f.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`),Ao(b&&b.map(R=>Object.assign({},R,{params:Object.assign({},s,R.params),pathname:Pe([u,n.encodeLocation?n.encodeLocation(R.pathname).pathname:R.pathname]),pathnameBase:R.pathnameBase==="/"?u:Pe([u,n.encodeLocation?n.encodeLocation(R.pathnameBase).pathname:R.pathnameBase])})),o,r,a)}function Do(){let e=gr(),t=ze(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,a="rgba(200,200,200, 0.5)",n={padding:"0.5rem",backgroundColor:a},o={padding:"2px 4px",backgroundColor:a},l=null;return console.error("Error handled by React Router default ErrorBoundary:",e),l=h.createElement(h.Fragment,null,h.createElement("p",null,"💿 Hey developer 👋"),h.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",h.createElement("code",{style:o},"ErrorBoundary")," or"," ",h.createElement("code",{style:o},"errorElement")," prop on your route.")),h.createElement(h.Fragment,null,h.createElement("h2",null,"Unexpected Application Error!"),h.createElement("h3",{style:{fontStyle:"italic"}},t),r?h.createElement("pre",{style:n},r):null,l)}var Oo=h.createElement(Do,null),ko=class extends h.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?h.createElement(Se.Provider,{value:this.props.routeContext},h.createElement(pr.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function No({routeContext:e,match:t,children:r}){let a=h.useContext(Be);return a&&a.static&&a.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=t.route.id),h.createElement(Se.Provider,{value:e},r)}function Ao(e,t=[],r=null,a=null){if(e==null){if(!r)return null;if(r.errors)e=r.matches;else if(t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let n=e,o=r==null?void 0:r.errors;if(o!=null){let i=n.findIndex(u=>u.route.id&&(o==null?void 0:o[u.route.id])!==void 0);J(i>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(o).join(",")}`),n=n.slice(0,Math.min(n.length,i+1))}let l=!1,s=-1;if(r)for(let i=0;i<n.length;i++){let u=n[i];if((u.route.HydrateFallback||u.route.hydrateFallbackElement)&&(s=i),u.route.id){let{loaderData:c,errors:p}=r,f=u.route.loader&&!c.hasOwnProperty(u.route.id)&&(!p||p[u.route.id]===void 0);if(u.route.lazy||f){l=!0,s>=0?n=n.slice(0,s+1):n=[n[0]];break}}}return n.reduceRight((i,u,c)=>{let p,f=!1,g=null,w=null;r&&(p=o&&u.route.id?o[u.route.id]:void 0,g=u.route.errorElement||Oo,l&&(s<0&&c===0?(Fn("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),f=!0,w=null):s===c&&(f=!0,w=u.route.hydrateFallbackElement||null)));let b=t.concat(n.slice(0,c+1)),S=()=>{let R;return p?R=g:f?R=w:u.route.Component?R=h.createElement(u.route.Component,null):u.route.element?R=u.route.element:R=i,h.createElement(No,{match:u,routeContext:{outlet:i,matches:b,isDataRoute:r!=null},children:R})};return r&&(u.route.ErrorBoundary||u.route.errorElement||c===0)?h.createElement(ko,{location:r.location,revalidation:r.revalidation,component:g,error:p,children:S(),routeContext:{outlet:null,matches:b,isDataRoute:!0}}):S()},null)}function yr(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Io(e){let t=h.useContext(Be);return J(t,yr(e)),t}function ft(e){let t=h.useContext(Ge);return J(t,yr(e)),t}function $o(e){let t=h.useContext(Se);return J(t,yr(e)),t}function ht(e){let t=$o(e),r=t.matches[t.matches.length-1];return J(r.route.id,`${e} can only be used on routes that contain a unique "id"`),r.route.id}function Fo(){return ht("useRouteId")}function jo(){return ft("useNavigation").navigation}function vr(){let{matches:e,loaderData:t}=ft("useMatches");return h.useMemo(()=>e.map(r=>vn(r,t)),[e,t])}function In(){let e=ft("useLoaderData"),t=ht("useLoaderData");return e.loaderData[t]}function $n(){let e=ft("useActionData"),t=ht("useLoaderData");return e.actionData?e.actionData[t]:void 0}function gr(){var a;let e=h.useContext(pr),t=ft("useRouteError"),r=ht("useRouteError");return e!==void 0?e:(a=t.errors)==null?void 0:a[r]}function Ho(){let{router:e}=Io("useNavigate"),t=ht("useNavigate"),r=h.useRef(!1);return kn(()=>{r.current=!0}),h.useCallback(async(n,o={})=>{re(r.current,On),r.current&&(typeof n=="number"?e.navigate(n):await e.navigate(n,{fromRouteId:t,...o}))},[e,t])}var ln={};function Fn(e,t,r){!t&&!ln[e]&&(ln[e]=!0,re(!1,r))}var sn={};function un(e,t){!e&&!sn[t]&&(sn[t]=!0,console.warn(t))}function pl(e){let t={hasErrorBoundary:e.hasErrorBoundary||e.ErrorBoundary!=null||e.errorElement!=null};return e.Component&&(e.element&&re(!1,"You should not include both `Component` and `element` on your route - `Component` will be used."),Object.assign(t,{element:h.createElement(e.Component),Component:void 0})),e.HydrateFallback&&(e.hydrateFallbackElement&&re(!1,"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used."),Object.assign(t,{hydrateFallbackElement:h.createElement(e.HydrateFallback),HydrateFallback:void 0})),e.ErrorBoundary&&(e.errorElement&&re(!1,"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used."),Object.assign(t,{errorElement:h.createElement(e.ErrorBoundary),ErrorBoundary:void 0})),t}var yl=["HydrateFallback","hydrateFallbackElement"],Uo=class{constructor(){this.status="pending",this.promise=new Promise((e,t)=>{this.resolve=r=>{this.status==="pending"&&(this.status="resolved",e(r))},this.reject=r=>{this.status==="pending"&&(this.status="rejected",t(r))}})}};function vl({router:e,flushSync:t}){let[r,a]=h.useState(e.state),[n,o]=h.useState(),[l,s]=h.useState({isTransitioning:!1}),[i,u]=h.useState(),[c,p]=h.useState(),[f,g]=h.useState(),w=h.useRef(new Map),b=h.useCallback((T,{deletedFetchers:M,flushSync:x,viewTransitionOpts:y})=>{T.fetchers.forEach((U,j)=>{U.data!==void 0&&w.current.set(j,U.data)}),M.forEach(U=>w.current.delete(U)),un(x===!1||t!=null,'You provided the `flushSync` option to a router update, but you are not using the `<RouterProvider>` from `react-router/dom` so `ReactDOM.flushSync()` is unavailable.  Please update your app to `import { RouterProvider } from "react-router/dom"` and ensure you have `react-dom` installed as a dependency to use the `flushSync` option.');let D=e.window!=null&&e.window.document!=null&&typeof e.window.document.startViewTransition=="function";if(un(y==null||D,"You provided the `viewTransition` option to a router update, but you do not appear to be running in a DOM environment as `window.startViewTransition` is not available."),!y||!D){t&&x?t(()=>a(T)):h.startTransition(()=>a(T));return}if(t&&x){t(()=>{c&&(i&&i.resolve(),c.skipTransition()),s({isTransitioning:!0,flushSync:!0,currentLocation:y.currentLocation,nextLocation:y.nextLocation})});let U=e.window.document.startViewTransition(()=>{t(()=>a(T))});U.finished.finally(()=>{t(()=>{u(void 0),p(void 0),o(void 0),s({isTransitioning:!1})})}),t(()=>p(U));return}c?(i&&i.resolve(),c.skipTransition(),g({state:T,currentLocation:y.currentLocation,nextLocation:y.nextLocation})):(o(T),s({isTransitioning:!0,flushSync:!1,currentLocation:y.currentLocation,nextLocation:y.nextLocation}))},[e.window,t,c,i]);h.useLayoutEffect(()=>e.subscribe(b),[e,b]),h.useEffect(()=>{l.isTransitioning&&!l.flushSync&&u(new Uo)},[l]),h.useEffect(()=>{if(i&&n&&e.window){let T=n,M=i.promise,x=e.window.document.startViewTransition(async()=>{h.startTransition(()=>a(T)),await M});x.finished.finally(()=>{u(void 0),p(void 0),o(void 0),s({isTransitioning:!1})}),p(x)}},[n,i,e.window]),h.useEffect(()=>{i&&n&&r.location.key===n.location.key&&i.resolve()},[i,c,r.location,n]),h.useEffect(()=>{!l.isTransitioning&&f&&(o(f.state),s({isTransitioning:!0,flushSync:!1,currentLocation:f.currentLocation,nextLocation:f.nextLocation}),g(void 0))},[l.isTransitioning,f]);let S=h.useMemo(()=>({createHref:e.createHref,encodeLocation:e.encodeLocation,go:T=>e.navigate(T),push:(T,M,x)=>e.navigate(T,{state:M,preventScrollReset:x==null?void 0:x.preventScrollReset}),replace:(T,M,x)=>e.navigate(T,{replace:!0,state:M,preventScrollReset:x==null?void 0:x.preventScrollReset})}),[e]),R=e.basename||"/",O=h.useMemo(()=>({router:e,navigator:S,static:!1,basename:R}),[e,S,R]);return h.createElement(h.Fragment,null,h.createElement(Be.Provider,{value:O},h.createElement(Ge.Provider,{value:r},h.createElement(Dn.Provider,{value:w.current},h.createElement(mr.Provider,{value:l},h.createElement(Wo,{basename:R,location:r.location,navigationType:r.historyAction,navigator:S},h.createElement(zo,{routes:e.routes,future:e.future,state:r})))))),null)}var zo=h.memo(Bo);function Bo({routes:e,future:t,state:r}){return _o(e,void 0,r,t)}function gl({to:e,replace:t,state:r,relative:a}){J(Xe(),"<Navigate> may be used only in the context of a <Router> component.");let{static:n}=h.useContext(we);re(!n,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:o}=h.useContext(Se),{pathname:l}=xe(),s=Nn(),i=Nt(e,kt(o),l,a==="path"),u=JSON.stringify(i);return h.useEffect(()=>{s(JSON.parse(u),{replace:t,state:r,relative:a})},[s,u,a,t,r]),null}function wl(e){return Mo(e.context)}function Wo({basename:e="/",children:t=null,location:r,navigationType:a="POP",navigator:n,static:o=!1}){J(!Xe(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let l=e.replace(/^\/*/,"/"),s=h.useMemo(()=>({basename:l,navigator:n,static:o,future:{}}),[l,n,o]);typeof r=="string"&&(r=Ae(r));let{pathname:i="/",search:u="",hash:c="",state:p=null,key:f="default"}=r,g=h.useMemo(()=>{let w=ge(i,l);return w==null?null:{location:{pathname:w,search:u,hash:c,state:p,key:f},navigationType:a}},[l,i,u,c,p,f,a]);return re(g!=null,`<Router basename="${l}"> is not able to match the URL "${i}${u}${c}" because it does not start with the basename, so the <Router> won't render anything.`),g==null?null:h.createElement(we.Provider,{value:s},h.createElement(At.Provider,{children:t,value:g}))}function El(e){return function(){const r={params:An(),loaderData:In(),actionData:$n(),matches:vr()};return h.createElement(e,r)}}function Rl(e){return function(){const r={params:An(),loaderData:In(),actionData:$n(),error:gr()};return h.createElement(e,r)}}var Lt="get",Ct="application/x-www-form-urlencoded";function It(e){return e!=null&&typeof e.tagName=="string"}function Yo(e){return It(e)&&e.tagName.toLowerCase()==="button"}function Vo(e){return It(e)&&e.tagName.toLowerCase()==="form"}function Jo(e){return It(e)&&e.tagName.toLowerCase()==="input"}function Go(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Xo(e,t){return e.button===0&&(!t||t==="_self")&&!Go(e)}var Et=null;function qo(){if(Et===null)try{new FormData(document.createElement("form"),0),Et=!1}catch{Et=!0}return Et}var Ko=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Kt(e){return e!=null&&!Ko.has(e)?(re(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Ct}"`),null):e}function Qo(e,t){let r,a,n,o,l;if(Vo(e)){let s=e.getAttribute("action");a=s?ge(s,t):null,r=e.getAttribute("method")||Lt,n=Kt(e.getAttribute("enctype"))||Ct,o=new FormData(e)}else if(Yo(e)||Jo(e)&&(e.type==="submit"||e.type==="image")){let s=e.form;if(s==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let i=e.getAttribute("formaction")||s.getAttribute("action");if(a=i?ge(i,t):null,r=e.getAttribute("formmethod")||s.getAttribute("method")||Lt,n=Kt(e.getAttribute("formenctype"))||Kt(s.getAttribute("enctype"))||Ct,o=new FormData(s,e),!qo()){let{name:u,type:c,value:p}=e;if(c==="image"){let f=u?`${u}.`:"";o.append(`${f}x`,"0"),o.append(`${f}y`,"0")}else u&&o.append(u,p)}}else{if(It(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=Lt,a=null,n=Ct,l=e}return o&&n==="text/plain"&&(l=o,o=void 0),{action:a,method:r.toLowerCase(),encType:n,formData:o,body:l}}function me(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}async function jn(e,t){if(e.id in t)return t[e.id];try{let r=await import(e.module);return t[e.id]=r,r}catch(r){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Zo(e,t,r){let a=e.map(o=>{var i;let l=t[o.route.id],s=r.routes[o.route.id];return[s&&s.css?s.css.map(u=>({rel:"stylesheet",href:u})):[],((i=l==null?void 0:l.links)==null?void 0:i.call(l))||[]]}).flat(2),n=Er(e,r);return Bn(a,n)}function Hn(e){return e.css?e.css.map(t=>({rel:"stylesheet",href:t})):[]}async function ei(e){if(!e.css)return;let t=Hn(e);await Promise.all(t.map(zn))}async function Un(e,t){if(!e.css&&!t.links||!oi())return;let r=[];if(e.css&&r.push(...Hn(e)),t.links&&r.push(...t.links()),r.length===0)return;let a=[];for(let n of r)!wr(n)&&n.rel==="stylesheet"&&a.push({...n,rel:"preload",as:"style"});await Promise.all(a.map(zn))}async function zn(e){return new Promise(t=>{if(e.media&&!window.matchMedia(e.media).matches||document.querySelector(`link[rel="stylesheet"][href="${e.href}"]`))return t();let r=document.createElement("link");Object.assign(r,e);function a(){document.head.contains(r)&&document.head.removeChild(r)}r.onload=()=>{a(),t()},r.onerror=()=>{a(),t()},document.head.appendChild(r)})}function wr(e){return e!=null&&typeof e.page=="string"}function ti(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function ri(e,t,r){let a=await Promise.all(e.map(async n=>{let o=t.routes[n.route.id];if(o){let l=await jn(o,r);return l.links?l.links():[]}return[]}));return Bn(a.flat(1).filter(ti).filter(n=>n.rel==="stylesheet"||n.rel==="preload").map(n=>n.rel==="stylesheet"?{...n,rel:"prefetch",as:"style"}:{...n,rel:"prefetch"}))}function cn(e,t,r,a,n,o){let l=(i,u)=>r[u]?i.route.id!==r[u].route.id:!0,s=(i,u)=>{var c;return r[u].pathname!==i.pathname||((c=r[u].route.path)==null?void 0:c.endsWith("*"))&&r[u].params["*"]!==i.params["*"]};return o==="assets"?t.filter((i,u)=>l(i,u)||s(i,u)):o==="data"?t.filter((i,u)=>{var p;let c=a.routes[i.route.id];if(!c||!c.hasLoader)return!1;if(l(i,u)||s(i,u))return!0;if(i.route.shouldRevalidate){let f=i.route.shouldRevalidate({currentUrl:new URL(n.pathname+n.search+n.hash,window.origin),currentParams:((p=r[0])==null?void 0:p.params)||{},nextUrl:new URL(e,window.origin),nextParams:i.params,defaultShouldRevalidate:!0});if(typeof f=="boolean")return f}return!0}):[]}function Er(e,t,{includeHydrateFallback:r}={}){return ni(e.map(a=>{let n=t.routes[a.route.id];if(!n)return[];let o=[n.module];return n.clientActionModule&&(o=o.concat(n.clientActionModule)),n.clientLoaderModule&&(o=o.concat(n.clientLoaderModule)),r&&n.hydrateFallbackModule&&(o=o.concat(n.hydrateFallbackModule)),n.imports&&(o=o.concat(n.imports)),o}).flat(1))}function ni(e){return[...new Set(e)]}function ai(e){let t={},r=Object.keys(e).sort();for(let a of r)t[a]=e[a];return t}function Bn(e,t){let r=new Set,a=new Set(t);return e.reduce((n,o)=>{if(t&&!wr(o)&&o.as==="script"&&o.href&&a.has(o.href))return n;let s=JSON.stringify(ai(o));return r.has(s)||(r.add(s),n.push({key:s,link:o})),n},[])}var Rt;function oi(){if(Rt!==void 0)return Rt;let e=document.createElement("link");return Rt=e.relList.supports("preload"),e=null,Rt}function dn(e){return{__html:e}}var ii=-1,li=-2,si=-3,ui=-4,ci=-5,di=-6,fi=-7,hi="B",mi="D",Wn="E",pi="M",yi="N",Yn="P",vi="R",gi="S",wi="Y",Ei="U",Ri="Z",Vn=class{constructor(){this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}};function bi(){const e=new TextDecoder;let t="";return new TransformStream({transform(r,a){const n=e.decode(r,{stream:!0}),o=(t+n).split(`
`);t=o.pop()||"";for(const l of o)a.enqueue(l)},flush(r){t&&r.enqueue(t)}})}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var Qt=typeof window<"u"?window:typeof globalThis<"u"?globalThis:void 0;function ir(e){const{hydrated:t,values:r}=this;if(typeof e=="number")return fn.call(this,e);if(!Array.isArray(e)||!e.length)throw new SyntaxError;const a=r.length;for(const n of e)r.push(n);return t.length=r.length,fn.call(this,a)}function fn(e){const{hydrated:t,values:r,deferred:a,plugins:n}=this;let o;const l=[[e,i=>{o=i}]];let s=[];for(;l.length>0;){const[i,u]=l.pop();switch(i){case fi:u(void 0);continue;case ci:u(null);continue;case li:u(NaN);continue;case di:u(1/0);continue;case si:u(-1/0);continue;case ui:u(-0);continue}if(t[i]){u(t[i]);continue}const c=r[i];if(!c||typeof c!="object"){t[i]=c,u(c);continue}if(Array.isArray(c))if(typeof c[0]=="string"){const[p,f,g]=c;switch(p){case mi:u(t[i]=new Date(f));continue;case Ei:u(t[i]=new URL(f));continue;case hi:u(t[i]=BigInt(f));continue;case vi:u(t[i]=new RegExp(f,g));continue;case wi:u(t[i]=Symbol.for(f));continue;case gi:const w=new Set;t[i]=w;for(let M=c.length-1;M>0;M--)l.push([c[M],x=>{w.add(x)}]);u(w);continue;case pi:const b=new Map;t[i]=b;for(let M=c.length-2;M>0;M-=2){const x=[];l.push([c[M+1],y=>{x[1]=y}]),l.push([c[M],y=>{x[0]=y}]),s.push(()=>{b.set(x[0],x[1])})}u(b);continue;case yi:const S=Object.create(null);t[i]=S;for(const M of Object.keys(f).reverse()){const x=[];l.push([f[M],y=>{x[1]=y}]),l.push([Number(M.slice(1)),y=>{x[0]=y}]),s.push(()=>{S[x[0]]=x[1]})}u(S);continue;case Yn:if(t[f])u(t[i]=t[f]);else{const M=new Vn;a[f]=M,u(t[i]=M.promise)}continue;case Wn:const[,R,O]=c;let T=O&&Qt&&Qt[O]?new Qt[O](R):new Error(R);t[i]=T,u(T);continue;case Ri:u(t[i]=t[f]);continue;default:if(Array.isArray(n)){const M=[],x=c.slice(1);for(let y=0;y<x.length;y++){const D=x[y];l.push([D,U=>{M[y]=U}])}s.push(()=>{for(const y of n){const D=y(c[0],...M);if(D){u(t[i]=D.value);return}}throw new SyntaxError});continue}throw new SyntaxError}}else{const p=[];t[i]=p;for(let f=0;f<c.length;f++){const g=c[f];g!==ii&&l.push([g,w=>{p[f]=w}])}u(p);continue}else{const p={};t[i]=p;for(const f of Object.keys(c).reverse()){const g=[];l.push([c[f],w=>{g[1]=w}]),l.push([Number(f.slice(1)),w=>{g[0]=w}]),s.push(()=>{p[g[0]]=g[1]})}u(p);continue}}for(;s.length>0;)s.pop()();return o}async function Si(e,t){const{plugins:r}=t??{},a=new Vn,n=e.pipeThrough(bi()).getReader(),o={values:[],hydrated:[],deferred:{},plugins:r},l=await xi.call(o,n);let s=a.promise;return l.done?a.resolve():s=Li.call(o,n).then(a.resolve).catch(i=>{for(const u of Object.values(o.deferred))u.reject(i);a.reject(i)}),{done:s.then(()=>n.closed),value:l.value}}async function xi(e){const t=await e.read();if(!t.value)throw new SyntaxError;let r;try{r=JSON.parse(t.value)}catch{throw new SyntaxError}return{done:t.done,value:ir.call(this,r)}}async function Li(e){let t=await e.read();for(;!t.done;){if(!t.value)continue;const r=t.value;switch(r[0]){case Yn:{const a=r.indexOf(":"),n=Number(r.slice(1,a)),o=this.deferred[n];if(!o)throw new Error(`Deferred ID ${n} not found in stream`);const l=r.slice(a+1);let s;try{s=JSON.parse(l)}catch{throw new SyntaxError}const i=ir.call(this,s);o.resolve(i);break}case Wn:{const a=r.indexOf(":"),n=Number(r.slice(1,a)),o=this.deferred[n];if(!o)throw new Error(`Deferred ID ${n} not found in stream`);const l=r.slice(a+1);let s;try{s=JSON.parse(l)}catch{throw new SyntaxError}const i=ir.call(this,s);o.reject(i);break}default:throw new SyntaxError}t=await e.read()}}async function Ci(e){let t={signal:e.signal};if(e.method!=="GET"){t.method=e.method;let r=e.headers.get("Content-Type");r&&/\bapplication\/json\b/.test(r)?(t.headers={"Content-Type":r},t.body=JSON.stringify(await e.json())):r&&/\btext\/plain\b/.test(r)?(t.headers={"Content-Type":r},t.body=await e.text()):r&&/\bapplication\/x-www-form-urlencoded\b/.test(r)?t.body=new URLSearchParams(await e.text()):t.body=await e.formData()}return t}var lr=Symbol("SingleFetchRedirect"),Jn=class extends Error{},Pi=202,Gn=new Set([100,101,204,205]);function bl(e,t,r,a,n){let o=Ti(e,l=>{let s=t.routes[l.route.id];me(s,"Route not found in manifest");let i=r[l.route.id];return{hasLoader:s.hasLoader,hasClientLoader:s.hasClientLoader,hasShouldRevalidate:!!(i!=null&&i.shouldRevalidate)}},Ai,a,n);return async l=>l.unstable_runClientMiddleware(o)}function Ti(e,t,r,a,n){return async o=>{let{request:l,matches:s,fetcherKey:i}=o,u=e();if(l.method!=="GET")return Mi(o,r,n);let c=s.some(p=>{let{hasLoader:f,hasClientLoader:g}=t(p);return p.unstable_shouldCallHandler()&&f&&!g});return!a&&!c?_i(o,t,r,n):i?ki(o,r,n):Di(o,u,t,r,a,n)}}async function Mi(e,t,r){let a=e.matches.find(l=>l.unstable_shouldCallHandler());me(a,"No action match found");let n,o=await a.resolve(async l=>await l(async()=>{let{data:i,status:u}=await t(e,r,[a.route.id]);return n=u,ct(i,a.route.id)}));return fr(o.result)||ze(o.result)||or(o.result)?{[a.route.id]:o}:{[a.route.id]:{type:o.type,result:Ka(o.result,n)}}}async function _i(e,t,r,a){let n=e.matches.filter(l=>l.unstable_shouldCallHandler()),o={};return await Promise.all(n.map(l=>l.resolve(async s=>{try{let{hasClientLoader:i}=t(l),u=l.route.id,c=i?await s(async()=>{let{data:p}=await r(e,a,[u]);return ct(p,u)}):await s();o[l.route.id]={type:"data",result:c}}catch(i){o[l.route.id]={type:"error",result:i}}}))),o}async function Di(e,t,r,a,n,o){let l=new Set,s=!1,i=e.matches.map(()=>hn()),u=hn(),c={},p=Promise.all(e.matches.map(async(g,w)=>g.resolve(async b=>{i[w].resolve();let S=g.route.id,{hasLoader:R,hasClientLoader:O,hasShouldRevalidate:T}=r(g),M=!g.unstable_shouldRevalidateArgs||g.unstable_shouldRevalidateArgs.actionStatus==null||g.unstable_shouldRevalidateArgs.actionStatus<400;if(!g.unstable_shouldCallHandler(M)){s||(s=g.unstable_shouldRevalidateArgs!=null&&R&&T===!0);return}if(O){R&&(s=!0);try{let y=await b(async()=>{let{data:D}=await a(e,o,[S]);return ct(D,S)});c[S]={type:"data",result:y}}catch(y){c[S]={type:"error",result:y}}return}R&&l.add(S);try{let y=await b(async()=>{let D=await u.promise;return ct(D,S)});c[S]={type:"data",result:y}}catch(y){c[S]={type:"error",result:y}}})));if(await Promise.all(i.map(g=>g.promise)),(!t.state.initialized&&t.state.navigation.state==="idle"||l.size===0)&&!window.__reactRouterHdrActive)u.resolve({routes:{}});else{let g=n&&s&&l.size>0?[...l.keys()]:void 0;try{let w=await a(e,o,g);u.resolve(w.data)}catch(w){u.reject(w)}}return await p,await Oi(u.promise,e.matches,l,c),c}async function Oi(e,t,r,a){try{let n,o=await e;if("routes"in o){for(let l of t)if(l.route.id in o.routes){let s=o.routes[l.route.id];if("error"in s){n=s.error;break}}}n!==void 0&&Array.from(r.values()).forEach(l=>{a[l].result instanceof Jn&&(a[l].result=n)})}catch{}}async function ki(e,t,r){let a=e.matches.find(l=>l.unstable_shouldCallHandler());me(a,"No fetcher match found");let n=a.route.id,o=await a.resolve(async l=>l(async()=>{let{data:s}=await t(e,r,[n]);return ct(s,n)}));return{[a.route.id]:o}}function Ni(e){let t=e.searchParams.getAll("index");e.searchParams.delete("index");let r=[];for(let a of t)a&&r.push(a);for(let a of r)e.searchParams.append("index",a);return e}function Xn(e,t){let r=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return r.pathname==="/"?r.pathname="_root.data":t&&ge(r.pathname,t)==="/"?r.pathname=`${t.replace(/\/$/,"")}/_root.data`:r.pathname=`${r.pathname.replace(/\/$/,"")}.data`,r}async function Ai(e,t,r){let{request:a}=e,n=Xn(a.url,t);a.method==="GET"&&(n=Ni(n),r&&n.searchParams.set("_routes",r.join(",")));let o=await fetch(n,await Ci(a));if(o.status===404&&!o.headers.has("X-Remix-Response"))throw new Ne(404,"Not Found",!0);if(o.status===204&&o.headers.has("X-Remix-Redirect"))return{status:Pi,data:{redirect:{redirect:o.headers.get("X-Remix-Redirect"),status:Number(o.headers.get("X-Remix-Status")||"302"),revalidate:o.headers.get("X-Remix-Revalidate")==="true",reload:o.headers.get("X-Remix-Reload-Document")==="true",replace:o.headers.get("X-Remix-Replace")==="true"}}};if(Gn.has(o.status)){let l={};return r&&a.method!=="GET"&&(l[r[0]]={data:void 0}),{status:o.status,data:{routes:l}}}me(o.body,"No response body to decode");try{let l=await Ii(o.body,window),s;if(a.method==="GET"){let i=l.value;lr in i?s={redirect:i[lr]}:s={routes:i}}else{let i=l.value,u=r==null?void 0:r[0];me(u,"No routeId found for single fetch call decoding"),"redirect"in i?s={redirect:i}:s={routes:{[u]:i}}}return{status:o.status,data:s}}catch{throw new Error("Unable to decode turbo-stream response")}}function Ii(e,t){return Si(e,{plugins:[(r,...a)=>{if(r==="SanitizedError"){let[n,o,l]=a,s=Error;n&&n in t&&typeof t[n]=="function"&&(s=t[n]);let i=new s(o);return i.stack=l,{value:i}}if(r==="ErrorResponse"){let[n,o,l]=a;return{value:new Ne(o,l,n)}}if(r==="SingleFetchRedirect")return{value:{[lr]:a[0]}};if(r==="SingleFetchClassInstance")return{value:a[0]};if(r==="SingleFetchFallback")return{value:void 0}}]})}function ct(e,t){if("redirect"in e){let{redirect:a,revalidate:n,reload:o,replace:l,status:s}=e.redirect;throw Qa(a,{status:s,headers:{...n?{"X-Remix-Revalidate":"yes"}:null,...o?{"X-Remix-Reload-Document":"yes"}:null,...l?{"X-Remix-Replace":"yes"}:null}})}let r=e.routes[t];if(r==null)throw new Jn(`No result found for routeId "${t}"`);if("error"in r)throw r.error;if("data"in r)return r.data;throw new Error(`Invalid response found for routeId "${t}"`)}function hn(){let e,t,r=new Promise((a,n)=>{e=async o=>{a(o);try{await r}catch{}},t=async o=>{n(o);try{await r}catch{}}});return{promise:r,resolve:e,reject:t}}var Sl=class extends h.Component{constructor(e){super(e),this.state={error:e.error||null,location:e.location}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location?{error:e.error||null,location:e.location}:{error:e.error||t.error,location:t.location}}render(){return this.state.error?h.createElement(qn,{error:this.state.error,isOutsideRemixApp:!0}):this.props.children}};function qn({error:e,isOutsideRemixApp:t}){console.error(e);let r=h.createElement("script",{dangerouslySetInnerHTML:{__html:`
        console.log(
          "💿 Hey developer 👋. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information."
        );
      `}});if(ze(e))return h.createElement(sr,{title:"Unhandled Thrown Response!"},h.createElement("h1",{style:{fontSize:"24px"}},e.status," ",e.statusText),r);let a;if(e instanceof Error)a=e;else{let n=e==null?"Unknown Error":typeof e=="object"&&"toString"in e?e.toString():JSON.stringify(e);a=new Error(n)}return h.createElement(sr,{title:"Application Error!",isOutsideRemixApp:t},h.createElement("h1",{style:{fontSize:"24px"}},"Application Error"),h.createElement("pre",{style:{padding:"2rem",background:"hsla(10, 50%, 50%, 0.1)",color:"red",overflow:"auto"}},a.stack),r)}function sr({title:e,renderScripts:t,isOutsideRemixApp:r,children:a}){var o;let{routeModules:n}=qe();return(o=n.root)!=null&&o.Layout&&!r?a:h.createElement("html",{lang:"en"},h.createElement("head",null,h.createElement("meta",{charSet:"utf-8"}),h.createElement("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),h.createElement("title",null,e)),h.createElement("body",null,h.createElement("main",{style:{fontFamily:"system-ui, sans-serif",padding:"2rem"}},a,t?h.createElement(Ki,null):null)))}function $i(){return h.createElement(sr,{title:"Loading...",renderScripts:!0},h.createElement("script",{dangerouslySetInnerHTML:{__html:`
              console.log(
                "💿 Hey developer 👋. You can provide a way better UX than this " +
                "when your app is loading JS modules and/or running \`clientLoader\` " +
                "functions. Check out https://reactrouter.com/start/framework/route-module#hydratefallback " +
                "for more information."
              );
            `}}))}function Kn(e){let t={};return Object.values(e).forEach(r=>{if(r){let a=r.parentId||"";t[a]||(t[a]=[]),t[a].push(r)}}),t}function Fi(e,t,r){let a=Qn(t),n=t.HydrateFallback&&(!r||e.id==="root")?t.HydrateFallback:e.id==="root"?$i:void 0,o=t.ErrorBoundary?t.ErrorBoundary:e.id==="root"?()=>h.createElement(qn,{error:gr()}):void 0;return e.id==="root"&&t.Layout?{...a?{element:h.createElement(t.Layout,null,h.createElement(a,null))}:{Component:a},...o?{errorElement:h.createElement(t.Layout,null,h.createElement(o,null))}:{ErrorBoundary:o},...n?{hydrateFallbackElement:h.createElement(t.Layout,null,h.createElement(n,null))}:{HydrateFallback:n}}:{Component:a,ErrorBoundary:o,HydrateFallback:n}}function xl(e,t,r,a,n,o){return Rr(t,r,a,n,o,"",Kn(t),e)}function bt(e,t){if(e==="loader"&&!t.hasLoader||e==="action"&&!t.hasAction){let a=`You are trying to call ${e==="action"?"serverAction()":"serverLoader()"} on a route that does not have a server ${e} (routeId: "${t.id}")`;throw console.error(a),new Ne(400,"Bad Request",new Error(a),!0)}}function Zt(e,t){let r=e==="clientAction"?"a":"an",a=`Route "${t}" does not have ${r} ${e}, but you are trying to submit to it. To fix this, please add ${r} \`${e}\` function to the route`;throw console.error(a),new Ne(405,"Method Not Allowed",new Error(a),!0)}function Rr(e,t,r,a,n,o="",l=Kn(e),s){return(l[o]||[]).map(i=>{var O,T,M;let u=t[i.id];function c(x){return me(typeof x=="function","No single fetch function available for route handler"),x()}function p(x){return i.hasLoader?c(x):Promise.resolve(null)}function f(x){if(!i.hasAction)throw Zt("action",i.id);return c(x)}function g(x){import(x)}function w(x){x.clientActionModule&&g(x.clientActionModule),x.clientLoaderModule&&g(x.clientLoaderModule)}async function b(x){let y=t[i.id],D=y?Un(i,y):Promise.resolve();try{return x()}finally{await D}}let S={id:i.id,index:i.index,path:i.path};if(u){Object.assign(S,{...S,...Fi(i,u,n),unstable_middleware:u.unstable_clientMiddleware,handle:u.handle,shouldRevalidate:mn(S.path,u,i,a,s)});let x=r&&r.loaderData&&i.id in r.loaderData,y=x?(O=r==null?void 0:r.loaderData)==null?void 0:O[i.id]:void 0,D=r&&r.errors&&i.id in r.errors,U=D?(T=r==null?void 0:r.errors)==null?void 0:T[i.id]:void 0,j=s==null&&(((M=u.clientLoader)==null?void 0:M.hydrate)===!0||!i.hasLoader);S.loader=async({request:K,params:ne,context:se},oe)=>{try{return await b(async()=>(me(u,"No `routeModule` available for critical-route loader"),u.clientLoader?u.clientLoader({request:K,params:ne,context:se,async serverLoader(){if(bt("loader",i),j){if(x)return y;if(D)throw U}return p(oe)}}):p(oe)))}finally{j=!1}},S.loader.hydrate=Zn(i.id,u.clientLoader,i.hasLoader,n),S.action=({request:K,params:ne,context:se},oe)=>b(async()=>{if(me(u,"No `routeModule` available for critical-route action"),!u.clientAction){if(n)throw Zt("clientAction",i.id);return f(oe)}return u.clientAction({request:K,params:ne,context:se,async serverAction(){return bt("action",i),f(oe)}})})}else{i.hasClientLoader||(S.loader=(D,U)=>b(()=>p(U))),i.hasClientAction||(S.action=(D,U)=>b(()=>{if(n)throw Zt("clientAction",i.id);return f(U)}));let x;async function y(){return x?await x:(x=(async()=>{(i.clientLoaderModule||i.clientActionModule)&&await new Promise(U=>setTimeout(U,0));let D=Hi(i,t);return w(i),await D})(),await x)}S.lazy={loader:i.hasClientLoader?async()=>{let{clientLoader:D}=i.clientLoaderModule?await import(i.clientLoaderModule):await y();return me(D,"No `clientLoader` export found"),(U,j)=>D({...U,async serverLoader(){return bt("loader",i),p(j)}})}:void 0,action:i.hasClientAction?async()=>{let D=i.clientActionModule?import(i.clientActionModule):y();w(i);let{clientAction:U}=await D;return me(U,"No `clientAction` export found"),(j,K)=>U({...j,async serverAction(){return bt("action",i),f(K)}})}:void 0,unstable_middleware:i.hasClientMiddleware?async()=>{let{unstable_clientMiddleware:D}=i.clientMiddlewareModule?await import(i.clientMiddlewareModule):await y();return me(D,"No `unstable_clientMiddleware` export found"),D}:void 0,shouldRevalidate:async()=>{let D=await y();return mn(S.path,D,i,a,s)},handle:async()=>(await y()).handle,Component:async()=>(await y()).Component,ErrorBoundary:i.hasErrorBoundary?async()=>(await y()).ErrorBoundary:void 0}}let R=Rr(e,t,r,a,n,i.id,l,s);return R.length>0&&(S.children=R),S})}function mn(e,t,r,a,n){if(n)return ji(r.id,t.shouldRevalidate,n);if(!a&&r.hasLoader&&!r.hasClientLoader){let o=e?En(e)[1].map(s=>s.paramName):[];const l=s=>o.some(i=>s.currentParams[i]!==s.nextParams[i]);if(t.shouldRevalidate){let s=t.shouldRevalidate;return i=>s({...i,defaultShouldRevalidate:l(i)})}else return s=>l(s)}if(a&&t.shouldRevalidate){let o=t.shouldRevalidate;return l=>o({...l,defaultShouldRevalidate:!0})}return t.shouldRevalidate}function ji(e,t,r){let a=!1;return n=>a?t?t(n):n.defaultShouldRevalidate:(a=!0,r.has(e))}async function Hi(e,t){let r=jn(e,t),a=ei(e),n=await r;return await Promise.all([a,Un(e,n)]),{Component:Qn(n),ErrorBoundary:n.ErrorBoundary,unstable_clientMiddleware:n.unstable_clientMiddleware,clientAction:n.clientAction,clientLoader:n.clientLoader,handle:n.handle,links:n.links,meta:n.meta,shouldRevalidate:n.shouldRevalidate}}function Qn(e){if(e.default==null)return;if(!(typeof e.default=="object"&&Object.keys(e.default).length===0))return e.default}function Zn(e,t,r,a){return a&&e!=="root"||t!=null&&(t.hydrate===!0||r!==!0)}var Pt=new Set,Ui=1e3,Ot=new Set,zi=7680;function br(e,t){return e.mode==="lazy"&&t===!0}function Bi({sri:e,...t},r){let a=new Set(r.state.matches.map(s=>s.route.id)),n=r.state.location.pathname.split("/").filter(Boolean),o=["/"];for(n.pop();n.length>0;)o.push(`/${n.join("/")}`),n.pop();o.forEach(s=>{let i=Ce(r.routes,s,r.basename);i&&i.forEach(u=>a.add(u.route.id))});let l=[...a].reduce((s,i)=>Object.assign(s,{[i]:t.routes[i]}),{});return{...t,routes:l,sri:e?!0:void 0}}function Ll(e,t,r,a,n,o){if(br(a,r))return async({path:l,patch:s,signal:i,fetcherKey:u})=>{Ot.has(l)||await ea([l],u?window.location.href:l,e,t,r,n,o,a.manifestPath,s,i)}}function Cl(e,t,r,a,n,o){h.useEffect(()=>{var c,p;if(!br(n,a)||((p=(c=window.navigator)==null?void 0:c.connection)==null?void 0:p.saveData)===!0)return;function l(f){let g=f.tagName==="FORM"?f.getAttribute("action"):f.getAttribute("href");if(!g)return;let w=f.tagName==="A"?f.pathname:new URL(g,window.location.origin).pathname;Ot.has(w)||Pt.add(w)}async function s(){document.querySelectorAll("a[data-discover], form[data-discover]").forEach(l);let f=Array.from(Pt.keys()).filter(g=>Ot.has(g)?(Pt.delete(g),!1):!0);if(f.length!==0)try{await ea(f,null,t,r,a,o,e.basename,n.manifestPath,e.patchRoutes)}catch(g){console.error("Failed to fetch manifest patches",g)}}let i=Vi(s,100);s();let u=new MutationObserver(()=>i());return u.observe(document.documentElement,{subtree:!0,childList:!0,attributes:!0,attributeFilter:["data-discover","href","action"]}),()=>u.disconnect()},[a,o,t,r,e,n])}function Wi(e,t){let r=e||"/__manifest";return t==null?r:`${t}${r}`.replace(/\/+/g,"/")}var er="react-router-manifest-version";async function ea(e,t,r,a,n,o,l,s,i,u){let c=new URL(Wi(s,l),window.location.origin);if(e.sort().forEach(b=>c.searchParams.append("p",b)),c.searchParams.set("version",r.version),c.toString().length>zi){Pt.clear();return}let p;try{let b=await fetch(c,{signal:u});if(b.ok){if(b.status===204&&b.headers.has("X-Remix-Reload-Document")){if(!t){console.warn("Detected a manifest version mismatch during eager route discovery. The next navigation/fetch to an undiscovered route will result in a new document navigation to sync up with the latest manifest.");return}if(sessionStorage.getItem(er)===r.version){console.error("Unable to discover routes due to manifest version mismatch.");return}sessionStorage.setItem(er,r.version),window.location.href=t,console.warn("Detected manifest version mismatch, reloading..."),await new Promise(()=>{})}else if(b.status>=400)throw new Error(await b.text())}else throw new Error(`${b.status} ${b.statusText}`);sessionStorage.removeItem(er),p=await b.json()}catch(b){if(u!=null&&u.aborted)return;throw b}let f=new Set(Object.keys(r.routes)),g=Object.values(p).reduce((b,S)=>(S&&!f.has(S.id)&&(b[S.id]=S),b),{});Object.assign(r.routes,g),e.forEach(b=>Yi(b,Ot));let w=new Set;Object.values(g).forEach(b=>{b&&(!b.parentId||!g[b.parentId])&&w.add(b.parentId)}),w.forEach(b=>i(b||null,Rr(g,a,null,n,o,b)))}function Yi(e,t){if(t.size>=Ui){let r=t.values().next().value;t.delete(r)}t.add(e)}function Vi(e,t){let r;return(...a)=>{window.clearTimeout(r),r=window.setTimeout(()=>e(...a),t)}}function Sr(){let e=h.useContext(Be);return me(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function $t(){let e=h.useContext(Ge);return me(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var Ft=h.createContext(void 0);Ft.displayName="FrameworkContext";function qe(){let e=h.useContext(Ft);return me(e,"You must render this element inside a <HydratedRouter> element"),e}function Ji(e,t){let r=h.useContext(Ft),[a,n]=h.useState(!1),[o,l]=h.useState(!1),{onFocus:s,onBlur:i,onMouseEnter:u,onMouseLeave:c,onTouchStart:p}=t,f=h.useRef(null);h.useEffect(()=>{if(e==="render"&&l(!0),e==="viewport"){let b=R=>{R.forEach(O=>{l(O.isIntersecting)})},S=new IntersectionObserver(b,{threshold:.5});return f.current&&S.observe(f.current),()=>{S.disconnect()}}},[e]),h.useEffect(()=>{if(a){let b=setTimeout(()=>{l(!0)},100);return()=>{clearTimeout(b)}}},[a]);let g=()=>{n(!0)},w=()=>{n(!1),l(!1)};return r?e!=="intent"?[o,f,{}]:[o,f,{onFocus:it(s,g),onBlur:it(i,w),onMouseEnter:it(u,g),onMouseLeave:it(c,w),onTouchStart:it(p,g)}]:[!1,f,{}]}function it(e,t){return r=>{e&&e(r),r.defaultPrevented||t(r)}}function xr(e,t,r){if(r&&!Tt)return[e[0]];if(t){let a=e.findIndex(n=>t[n.route.id]!==void 0);return e.slice(0,a+1)}return e}function Pl(){let{isSpaMode:e,manifest:t,routeModules:r,criticalCss:a}=qe(),{errors:n,matches:o}=$t(),l=xr(o,n,e),s=h.useMemo(()=>Zo(l,r,t),[l,r,t]);return h.createElement(h.Fragment,null,typeof a=="string"?h.createElement("style",{dangerouslySetInnerHTML:{__html:a}}):null,typeof a=="object"?h.createElement("link",{rel:"stylesheet",href:a.href}):null,s.map(({key:i,link:u})=>wr(u)?h.createElement(ta,{key:i,...u}):h.createElement("link",{key:i,...u})))}function ta({page:e,...t}){let{router:r}=Sr(),a=h.useMemo(()=>Ce(r.routes,e,r.basename),[r.routes,e,r.basename]);return a?h.createElement(Xi,{page:e,matches:a,...t}):null}function Gi(e){let{manifest:t,routeModules:r}=qe(),[a,n]=h.useState([]);return h.useEffect(()=>{let o=!1;return ri(e,t,r).then(l=>{o||n(l)}),()=>{o=!0}},[e,t,r]),a}function Xi({page:e,matches:t,...r}){let a=xe(),{manifest:n,routeModules:o}=qe(),{basename:l}=Sr(),{loaderData:s,matches:i}=$t(),u=h.useMemo(()=>cn(e,t,i,n,a,"data"),[e,t,i,n,a]),c=h.useMemo(()=>cn(e,t,i,n,a,"assets"),[e,t,i,n,a]),p=h.useMemo(()=>{if(e===a.pathname+a.search+a.hash)return[];let w=new Set,b=!1;if(t.forEach(R=>{var T;let O=n.routes[R.route.id];!O||!O.hasLoader||(!u.some(M=>M.route.id===R.route.id)&&R.route.id in s&&((T=o[R.route.id])!=null&&T.shouldRevalidate)||O.hasClientLoader?b=!0:w.add(R.route.id))}),w.size===0)return[];let S=Xn(e,l);return b&&w.size>0&&S.searchParams.set("_routes",t.filter(R=>w.has(R.route.id)).map(R=>R.route.id).join(",")),[S.pathname+S.search]},[l,s,a,n,u,t,e,o]),f=h.useMemo(()=>Er(c,n),[c,n]),g=Gi(c);return h.createElement(h.Fragment,null,p.map(w=>h.createElement("link",{key:w,rel:"prefetch",as:"fetch",href:w,...r})),f.map(w=>h.createElement("link",{key:w,rel:"modulepreload",href:w,...r})),g.map(({key:w,link:b})=>h.createElement("link",{key:w,...b})))}function Tl(){let{isSpaMode:e,routeModules:t}=qe(),{errors:r,matches:a,loaderData:n}=$t(),o=xe(),l=xr(a,r,e),s=null;r&&(s=r[l[l.length-1].route.id]);let i=[],u=null,c=[];for(let p=0;p<l.length;p++){let f=l[p],g=f.route.id,w=n[g],b=f.params,S=t[g],R=[],O={id:g,data:w,meta:[],params:f.params,pathname:f.pathname,handle:f.route.handle,error:s};if(c[p]=O,S!=null&&S.meta?R=typeof S.meta=="function"?S.meta({data:w,params:b,location:o,matches:c,error:s}):Array.isArray(S.meta)?[...S.meta]:S.meta:u&&(R=[...u]),R=R||[],!Array.isArray(R))throw new Error("The route at "+f.route.path+` returns an invalid value. All route meta functions must return an array of meta objects.

To reference the meta function API, see https://remix.run/route/meta`);O.meta=R,c[p]=O,i=[...R],u=i}return h.createElement(h.Fragment,null,i.flat().map(p=>{if(!p)return null;if("tagName"in p){let{tagName:f,...g}=p;if(!qi(f))return console.warn(`A meta object uses an invalid tagName: ${f}. Expected either 'link' or 'meta'`),null;let w=f;return h.createElement(w,{key:JSON.stringify(g),...g})}if("title"in p)return h.createElement("title",{key:"title"},String(p.title));if("charset"in p&&(p.charSet??(p.charSet=p.charset),delete p.charset),"charSet"in p&&p.charSet!=null)return typeof p.charSet=="string"?h.createElement("meta",{key:"charSet",charSet:p.charSet}):null;if("script:ld+json"in p)try{let f=JSON.stringify(p["script:ld+json"]);return h.createElement("script",{key:`script:ld+json:${f}`,type:"application/ld+json",dangerouslySetInnerHTML:{__html:f}})}catch{return null}return h.createElement("meta",{key:JSON.stringify(p),...p})}))}function qi(e){return typeof e=="string"&&/^(meta|link)$/.test(e)}var Tt=!1;function Ki(e){let{manifest:t,serverHandoffString:r,isSpaMode:a,renderMeta:n,routeDiscovery:o,ssr:l}=qe(),{router:s,static:i,staticContext:u}=Sr(),{matches:c}=$t(),p=br(o,l);n&&(n.didRenderScripts=!0);let f=xr(c,null,a);h.useEffect(()=>{Tt=!0},[]);let g=h.useMemo(()=>{var T;let R=u?`window.__reactRouterContext = ${r};window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());`:" ",O=i?`${(T=t.hmr)!=null&&T.runtime?`import ${JSON.stringify(t.hmr.runtime)};`:""}${p?"":`import ${JSON.stringify(t.url)}`};
${f.map((M,x)=>{let y=`route${x}`,D=t.routes[M.route.id];me(D,`Route ${M.route.id} not found in manifest`);let{clientActionModule:U,clientLoaderModule:j,clientMiddlewareModule:K,hydrateFallbackModule:ne,module:se}=D,oe=[...U?[{module:U,varName:`${y}_clientAction`}]:[],...j?[{module:j,varName:`${y}_clientLoader`}]:[],...K?[{module:K,varName:`${y}_clientMiddleware`}]:[],...ne?[{module:ne,varName:`${y}_HydrateFallback`}]:[],{module:se,varName:`${y}_main`}];if(oe.length===1)return`import * as ${y} from ${JSON.stringify(se)};`;let X=oe.map(G=>`import * as ${G.varName} from "${G.module}";`).join(`
`),Q=`const ${y} = {${oe.map(G=>`...${G.varName}`).join(",")}};`;return[X,Q].join(`
`)}).join(`
`)}
  ${p?`window.__reactRouterManifest = ${JSON.stringify(Bi(t,s),null,2)};`:""}
  window.__reactRouterRouteModules = {${f.map((M,x)=>`${JSON.stringify(M.route.id)}:route${x}`).join(",")}};

import(${JSON.stringify(t.entry.module)});`:" ";return h.createElement(h.Fragment,null,h.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:dn(R),type:void 0}),h.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:dn(O),type:"module",async:!0}))},[]),w=Tt?[]:Qi(t.entry.imports.concat(Er(f,t,{includeHydrateFallback:!0}))),b=typeof t.sri=="object"?t.sri:{};return Tt?null:h.createElement(h.Fragment,null,typeof t.sri=="object"?h.createElement("script",{"rr-importmap":"",type:"importmap",suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:JSON.stringify({integrity:b})}}):null,p?null:h.createElement("link",{rel:"modulepreload",href:t.url,crossOrigin:e.crossOrigin,integrity:b[t.url],suppressHydrationWarning:!0}),h.createElement("link",{rel:"modulepreload",href:t.entry.module,crossOrigin:e.crossOrigin,integrity:b[t.entry.module],suppressHydrationWarning:!0}),w.map(S=>h.createElement("link",{key:S,rel:"modulepreload",href:S,crossOrigin:e.crossOrigin,integrity:b[S],suppressHydrationWarning:!0})),g)}function Qi(e){return[...new Set(e)]}function Zi(...e){return t=>{e.forEach(r=>{typeof r=="function"?r(t):r!=null&&(r.current=t)})}}var ra=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{ra&&(window.__reactRouterVersion="7.6.3")}catch{}var na=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,aa=h.forwardRef(function({onClick:t,discover:r="render",prefetch:a="none",relative:n,reloadDocument:o,replace:l,state:s,target:i,to:u,preventScrollReset:c,viewTransition:p,...f},g){let{basename:w}=h.useContext(we),b=typeof u=="string"&&na.test(u),S,R=!1;if(typeof u=="string"&&b&&(S=u,ra))try{let j=new URL(window.location.href),K=u.startsWith("//")?new URL(j.protocol+u):new URL(u),ne=ge(K.pathname,w);K.origin===j.origin&&ne!=null?u=ne+K.search+K.hash:R=!0}catch{re(!1,`<Link to="${u}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let O=Co(u,{relative:n}),[T,M,x]=Ji(a,f),y=al(u,{replace:l,state:s,target:i,preventScrollReset:c,relative:n,viewTransition:p});function D(j){t&&t(j),j.defaultPrevented||y(j)}let U=h.createElement("a",{...f,...x,href:S||O,onClick:R||o?t:D,ref:Zi(g,M),target:i,"data-discover":!b&&r==="render"?"true":void 0});return T&&!b?h.createElement(h.Fragment,null,U,h.createElement(ta,{page:O})):U});aa.displayName="Link";var el=h.forwardRef(function({"aria-current":t="page",caseSensitive:r=!1,className:a="",end:n=!1,style:o,to:l,viewTransition:s,children:i,...u},c){let p=dt(l,{relative:u.relative}),f=xe(),g=h.useContext(Ge),{navigator:w,basename:b}=h.useContext(we),S=g!=null&&dl(p)&&s===!0,R=w.encodeLocation?w.encodeLocation(p).pathname:p.pathname,O=f.pathname,T=g&&g.navigation&&g.navigation.location?g.navigation.location.pathname:null;r||(O=O.toLowerCase(),T=T?T.toLowerCase():null,R=R.toLowerCase()),T&&b&&(T=ge(T,b)||T);const M=R!=="/"&&R.endsWith("/")?R.length-1:R.length;let x=O===R||!n&&O.startsWith(R)&&O.charAt(M)==="/",y=T!=null&&(T===R||!n&&T.startsWith(R)&&T.charAt(R.length)==="/"),D={isActive:x,isPending:y,isTransitioning:S},U=x?t:void 0,j;typeof a=="function"?j=a(D):j=[a,x?"active":null,y?"pending":null,S?"transitioning":null].filter(Boolean).join(" ");let K=typeof o=="function"?o(D):o;return h.createElement(aa,{...u,"aria-current":U,className:j,ref:c,style:K,to:l,viewTransition:s},typeof i=="function"?i(D):i)});el.displayName="NavLink";var tl=h.forwardRef(({discover:e="render",fetcherKey:t,navigate:r,reloadDocument:a,replace:n,state:o,method:l=Lt,action:s,onSubmit:i,relative:u,preventScrollReset:c,viewTransition:p,...f},g)=>{let w=ll(),b=sl(s,{relative:u}),S=l.toLowerCase()==="get"?"get":"post",R=typeof s=="string"&&na.test(s),O=T=>{if(i&&i(T),T.defaultPrevented)return;T.preventDefault();let M=T.nativeEvent.submitter,x=(M==null?void 0:M.getAttribute("formmethod"))||l;w(M||T.currentTarget,{fetcherKey:t,method:x,navigate:r,replace:n,state:o,relative:u,preventScrollReset:c,viewTransition:p})};return h.createElement("form",{ref:g,method:S,action:b,onSubmit:a?i:O,...f,"data-discover":!R&&e==="render"?"true":void 0})});tl.displayName="Form";function rl({getKey:e,storageKey:t,...r}){let a=h.useContext(Ft),{basename:n}=h.useContext(we),o=xe(),l=vr();ul({getKey:e,storageKey:t});let s=h.useMemo(()=>{if(!a||!e)return null;let u=cr(o,l,n,e);return u!==o.key?u:null},[]);if(!a||a.isSpaMode)return null;let i=((u,c)=>{if(!window.history.state||!window.history.state.key){let p=Math.random().toString(32).slice(2);window.history.replaceState({key:p},"")}try{let f=JSON.parse(sessionStorage.getItem(u)||"{}")[c||window.history.state.key];typeof f=="number"&&window.scrollTo(0,f)}catch(p){console.error(p),sessionStorage.removeItem(u)}}).toString();return h.createElement("script",{...r,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:`(${i})(${JSON.stringify(t||ur)}, ${JSON.stringify(s)})`}})}rl.displayName="ScrollRestoration";function oa(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Lr(e){let t=h.useContext(Be);return J(t,oa(e)),t}function nl(e){let t=h.useContext(Ge);return J(t,oa(e)),t}function al(e,{target:t,replace:r,state:a,preventScrollReset:n,relative:o,viewTransition:l}={}){let s=Nn(),i=xe(),u=dt(e,{relative:o});return h.useCallback(c=>{if(Xo(c,t)){c.preventDefault();let p=r!==void 0?r:ke(i)===ke(u);s(e,{replace:p,state:a,preventScrollReset:n,relative:o,viewTransition:l})}},[i,s,u,r,a,t,e,n,o,l])}var ol=0,il=()=>`__${String(++ol)}__`;function ll(){let{router:e}=Lr("useSubmit"),{basename:t}=h.useContext(we),r=Fo();return h.useCallback(async(a,n={})=>{let{action:o,method:l,encType:s,formData:i,body:u}=Qo(a,t);if(n.navigate===!1){let c=n.fetcherKey||il();await e.fetch(c,r,n.action||o,{preventScrollReset:n.preventScrollReset,formData:i,body:u,formMethod:n.method||l,formEncType:n.encType||s,flushSync:n.flushSync})}else await e.navigate(n.action||o,{preventScrollReset:n.preventScrollReset,formData:i,body:u,formMethod:n.method||l,formEncType:n.encType||s,replace:n.replace,state:n.state,fromRouteId:r,flushSync:n.flushSync,viewTransition:n.viewTransition})},[e,t,r])}function sl(e,{relative:t}={}){let{basename:r}=h.useContext(we),a=h.useContext(Se);J(a,"useFormAction must be used inside a RouteContext");let[n]=a.matches.slice(-1),o={...dt(e||".",{relative:t})},l=xe();if(e==null){o.search=l.search;let s=new URLSearchParams(o.search),i=s.getAll("index");if(i.some(c=>c==="")){s.delete("index"),i.filter(p=>p).forEach(p=>s.append("index",p));let c=s.toString();o.search=c?`?${c}`:""}}return(!e||e===".")&&n.route.index&&(o.search=o.search?o.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(o.pathname=o.pathname==="/"?r:Pe([r,o.pathname])),ke(o)}var ur="react-router-scroll-positions",St={};function cr(e,t,r,a){let n=null;return a&&(r!=="/"?n=a({...e,pathname:ge(e.pathname,r)||e.pathname},t):n=a(e,t)),n==null&&(n=e.key),n}function ul({getKey:e,storageKey:t}={}){let{router:r}=Lr("useScrollRestoration"),{restoreScrollPosition:a,preventScrollReset:n}=nl("useScrollRestoration"),{basename:o}=h.useContext(we),l=xe(),s=vr(),i=jo();h.useEffect(()=>(window.history.scrollRestoration="manual",()=>{window.history.scrollRestoration="auto"}),[]),cl(h.useCallback(()=>{if(i.state==="idle"){let u=cr(l,s,o,e);St[u]=window.scrollY}try{sessionStorage.setItem(t||ur,JSON.stringify(St))}catch(u){re(!1,`Failed to save scroll positions in sessionStorage, <ScrollRestoration /> will not work properly (${u}).`)}window.history.scrollRestoration="auto"},[i.state,e,o,l,s,t])),typeof document<"u"&&(h.useLayoutEffect(()=>{try{let u=sessionStorage.getItem(t||ur);u&&(St=JSON.parse(u))}catch{}},[t]),h.useLayoutEffect(()=>{let u=r==null?void 0:r.enableScrollRestoration(St,()=>window.scrollY,e?(c,p)=>cr(c,p,o,e):void 0);return()=>u&&u()},[r,o,e]),h.useLayoutEffect(()=>{if(a!==!1){if(typeof a=="number"){window.scrollTo(0,a);return}if(l.hash){let u=document.getElementById(decodeURIComponent(l.hash.slice(1)));if(u){u.scrollIntoView();return}}n!==!0&&window.scrollTo(0,0)}},[l,a,n]))}function cl(e,t){let{capture:r}={};h.useEffect(()=>{let a=r!=null?{capture:r}:void 0;return window.addEventListener("pagehide",e,a),()=>{window.removeEventListener("pagehide",e,a)}},[e,r])}function dl(e,t={}){let r=h.useContext(mr);J(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:a}=Lr("useViewTransitionState"),n=dt(e,{relative:t.relative});if(!r.isTransitioning)return!1;let o=ge(r.currentLocation.pathname,a)||r.currentLocation.pathname,l=ge(r.nextLocation.pathname,a)||r.nextLocation.pathname;return _t(n.pathname,l)!=null||_t(n.pathname,o)!=null}[...Gn];function Ml(e){if(!e)return null;let t=Object.entries(e),r={};for(let[a,n]of t)if(n&&n.__type==="RouteErrorResponse")r[a]=new Ne(n.status,n.statusText,n.data,n.internal===!0);else if(n&&n.__type==="Error"){if(n.__subType){let o=window[n.__subType];if(typeof o=="function")try{let l=new o(n.message);l.stack=n.stack,r[a]=l}catch{}}if(r[a]==null){let o=new Error(n.message);o.stack=n.stack,r[a]=o}}else r[a]=n;return r}function _l(e,t,r,a,n,o){let l={...e,loaderData:{...e.loaderData}},s=Ce(t,a,n);if(s)for(let i of s){let u=i.route.id,c=r(u);Zn(u,c.clientLoader,c.hasLoader,o)&&(c.hasHydrateFallback||!c.hasLoader)?delete l.loaderData[u]:c.hasLoader||(l.loaderData[u]=null)}return l}export{Ft as F,Pl as L,Tl as M,gl as N,wl as O,Sl as R,rl as S,h as a,vl as b,Rr as c,Ii as d,Ml as e,ml as f,_l as g,Ll as h,J as i,bl as j,hl as k,xl as l,pl as m,yl as n,fl as o,Rl as p,Ki as q,Sa as r,ze as s,aa as t,Cl as u,An as v,El as w,Nn as x,xe as y};
