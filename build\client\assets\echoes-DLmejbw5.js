import{a as d,o as e,w}from"./chunk-QMGIS6GS-Dm1jN8z9.js";import{c as f,L as k,M as b}from"./Layout-B-k2_PuX.js";import{u as E,L as M,E as L}from"./Toast-DTYeq3xB.js";import{s as v}from"./AuthContext-C3ETFuYa.js";import{c as u}from"./user-BOgs6E2m.js";import{H as S}from"./heart-COttkdNw.js";import{f as _}from"./format-CBpsKyOP.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C=[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],N=u("clock",C);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],I=u("refresh-cw",D);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R=[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]],T=u("send",R);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $=[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]],H=u("sparkles",$);function g(){const[r,a]=d.useState([]),[c,o]=d.useState(!0),[i,l]=d.useState(null),x=async()=>{try{o(!0);const{data:s,error:t}=await v.from("echoes").select("*").gt("expires_at",new Date().toISOString()).order("created_at",{ascending:!1});if(t)throw t;a(s||[])}catch(s){l(s instanceof Error?s.message:"获取回音失败")}finally{o(!1)}},p=async s=>{try{if(s.length>500)return{error:"内容不能超过500字符"};const{data:t,error:n}=await v.from("echoes").insert([{content:s}]).select().single();if(n)throw n;return a(h=>[t,...h]),{data:t,error:null}}catch(t){const n=t instanceof Error?t.message:"发布回音失败";return l(n),{error:n}}},m=s=>{const t=new Date,h=new Date(s).getTime()-t.getTime();if(h<=0)return"已过期";const j=Math.floor(h/(1e3*60*60)),y=Math.floor(h%(1e3*60*60)/(1e3*60));return j>0?`${j}小时${y}分钟后消失`:`${y}分钟后消失`};return d.useEffect(()=>{x();const s=setInterval(()=>{a(t=>t.filter(n=>new Date(n.expires_at)>new Date))},6e4);return()=>clearInterval(s)},[]),{echoes:r,loading:c,error:i,createEcho:p,getTimeRemaining:m,refetch:x}}function z(){const[r,a]=d.useState(""),[c,o]=d.useState(!1),[i,l]=d.useState(""),{createEcho:x}=g(),{success:p,error:m}=E(),s=async t=>{if(t.preventDefault(),!r.trim()){l("请输入内容");return}if(r.length>500){l("内容不能超过500字符");return}o(!0),l("");const{error:n}=await x(r.trim());n?(l(n),m(n)):(a(""),p("回音已投递到长廊中")),o(!1)};return e.jsxs("div",{className:"bg-white/80 backdrop-blur-enhanced rounded-2xl p-6 shadow-lg mb-8 hover-lift",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"投递一段回音"}),e.jsxs("p",{className:"text-sm text-gray-600 flex items-center space-x-1",children:[e.jsx(N,{className:"w-4 h-4"}),e.jsx("span",{children:"匿名发布，24小时后自动消失"})]})]}),e.jsxs("form",{onSubmit:s,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("textarea",{value:r,onChange:t=>a(t.target.value),rows:4,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none",placeholder:"分享你的思绪、情感或感悟...如漫天蝶游般自由表达",maxLength:500}),e.jsxs("div",{className:"flex justify-between items-center mt-2",children:[e.jsxs("div",{className:"text-sm text-gray-500",children:[r.length,"/500 字符"]}),r.length>450&&e.jsx("div",{className:"text-sm text-orange-600",children:"即将达到字符限制"})]})]}),i&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm",children:i}),e.jsx("div",{className:"flex justify-end",children:e.jsxs("button",{type:"submit",disabled:c||!r.trim(),className:"flex items-center space-x-2 bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all transform hover:scale-105 btn-ripple",children:[e.jsx(T,{className:"w-5 h-5"}),e.jsx("span",{children:c?"发布中...":"投递回音"})]})})]})]})}function A({echo:r}){const{getTimeRemaining:a}=g(),[c,o]=d.useState(""),[i,l]=d.useState(!1);d.useEffect(()=>{const m=()=>{o(a(r.expires_at))};m();const s=setInterval(m,6e4);return()=>clearInterval(s)},[r.expires_at,a]);const x=()=>{l(!i)},p=()=>{const m=["from-purple-400 to-pink-400","from-blue-400 to-purple-400","from-pink-400 to-red-400","from-indigo-400 to-blue-400","from-purple-400 to-indigo-400","from-pink-400 to-purple-400"],s=r.id.split("").reduce((t,n)=>(t=(t<<5)-t+n.charCodeAt(0),t&t),0);return m[Math.abs(s)%m.length]};return e.jsxs("div",{className:"group relative",children:[e.jsx("div",{className:f("absolute inset-0 bg-gradient-to-br opacity-10 rounded-2xl transform rotate-1 group-hover:rotate-2 transition-transform",p())}),e.jsxs("div",{className:"relative bg-white/80 backdrop-blur-enhanced rounded-2xl p-6 shadow-lg hover-lift",children:[e.jsx("div",{className:"mb-4",children:e.jsx("p",{className:"text-gray-800 leading-relaxed whitespace-pre-wrap",children:r.content})}),e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsxs("div",{className:"flex items-center space-x-4 text-gray-500",children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(N,{className:"w-4 h-4"}),e.jsx("span",{children:c})]}),e.jsx("div",{children:_(new Date(r.created_at),"MM月dd日 HH:mm")})]}),e.jsxs("button",{onClick:x,className:f("flex items-center space-x-1 px-3 py-1 rounded-full transition-colors",i?"bg-red-100 text-red-600":"text-gray-500 hover:bg-gray-100 hover:text-red-500"),children:[e.jsx(S,{className:f("w-4 h-4",i&&"fill-current")}),e.jsx("span",{className:"text-xs",children:"共鸣"})]})]}),e.jsx("div",{className:"absolute top-4 right-4 opacity-20",children:e.jsx("div",{className:"w-8 h-8 rounded-full bg-gradient-to-br from-purple-400 to-pink-400 animate-pulse"})})]})]})}const K=w(function(){const{echoes:a,loading:c,error:o,refetch:i}=g();return e.jsx(k,{children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsxs("div",{className:"flex items-center justify-center space-x-3 mb-4",children:[e.jsx(b,{className:"w-8 h-8 text-purple-600 animate-float"}),e.jsx("h1",{className:"text-3xl font-bold gradient-text",children:"回音长廊"}),e.jsx(H,{className:"w-8 h-8 text-purple-600 animate-float",style:{animationDelay:"1s"}})]}),e.jsx("p",{className:"text-gray-600 max-w-2xl mx-auto leading-relaxed",children:'在这里匿名分享你的思绪与情感，如"漫天蝶游"般自由表达。 每一段回音都会在24小时后消失，象征着情感的流逝。 谁不是流泪的旁观者，在无声的共鸣中获得慰藉。'})]}),e.jsx(z,{}),e.jsx("div",{className:"flex justify-center mb-6",children:e.jsxs("button",{onClick:i,disabled:c,className:"flex items-center space-x-2 text-gray-600 hover:text-purple-600 transition-colors",children:[e.jsx(I,{className:`w-5 h-5 ${c?"animate-spin":""}`}),e.jsx("span",{children:"刷新回音"})]})}),c&&e.jsx("div",{className:"flex items-center justify-center py-12",children:e.jsx(M,{color:"purple",text:"聆听回音中..."})}),o&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6 backdrop-blur-enhanced",children:o}),!c&&!o&&a.length===0&&e.jsx(L,{icon:b,title:"长廊中还很安静",description:"成为第一个投递回音的人吧",className:"bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl"}),!c&&a.length>0&&e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"text-center mb-6",children:e.jsxs("p",{className:"text-gray-600",children:["共有 ",e.jsx("span",{className:"font-semibold text-purple-600",children:a.length})," 段回音在长廊中回响"]})}),e.jsx("div",{className:"grid gap-6",children:a.map(l=>e.jsx(A,{echo:l},l.id))})]}),e.jsxs("div",{className:"text-center mt-12 py-8 border-t border-gray-200",children:[e.jsx("blockquote",{className:"text-lg text-gray-700 italic leading-relaxed mb-2",children:'"物是人非谁不是流泪的旁观者"'}),e.jsx("p",{className:"text-gray-500",children:"— 在共鸣中寻找慰藉"})]})]})})});export{K as default};
