import{w as x,o as e,t as s}from"./chunk-QMGIS6GS-Dm1jN8z9.js";import{u as o}from"./AuthContext-C3ETFuYa.js";import{B as l,M as r,L as c}from"./Layout-B-k2_PuX.js";import{H as i}from"./heart-COttkdNw.js";import{c as n}from"./user-BOgs6E2m.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const m=[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]],d=n("star",m);function N({}){return[{title:"梦的出口 - 诗意的回忆与情感分享空间"},{name:"description",content:"在这里记录珍贵回忆，分享内心情感，寻找心灵的共鸣"}]}const f=x(function(){var a;const{user:t}=o();return t?e.jsx(c,{children:e.jsxs("div",{className:"text-center max-w-4xl mx-auto",children:[e.jsxs("div",{className:"mb-12",children:[e.jsxs("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:["欢迎回到梦境，",(a=t.email)==null?void 0:a.split("@")[0]]}),e.jsx("p",{className:"text-xl text-gray-600",children:"在这里记录美好，分享情感，寻找心灵的共鸣"})]}),e.jsxs("div",{className:"grid md:grid-cols-2 gap-8 mb-12",children:[e.jsxs(s,{to:"/journal",className:"group bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all transform hover:scale-105",children:[e.jsx(l,{className:"w-16 h-16 text-blue-500 mx-auto mb-6 group-hover:scale-110 transition-transform"}),e.jsx("h3",{className:"text-2xl font-semibold text-gray-900 mb-3",children:"时光书简"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"记录珍贵回忆，创建专属的情感档案"}),e.jsxs("div",{className:"flex items-center justify-center space-x-2 text-blue-500",children:[e.jsx("span",{children:"开始记录"}),e.jsx(i,{className:"w-4 h-4"})]})]}),e.jsxs(s,{to:"/echoes",className:"group bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all transform hover:scale-105",children:[e.jsx(r,{className:"w-16 h-16 text-purple-500 mx-auto mb-6 group-hover:scale-110 transition-transform"}),e.jsx("h3",{className:"text-2xl font-semibold text-gray-900 mb-3",children:"回音长廊"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"匿名分享情感，在共鸣中找到慰藉"}),e.jsxs("div",{className:"flex items-center justify-center space-x-2 text-purple-500",children:[e.jsx("span",{children:"探索长廊"}),e.jsx(d,{className:"w-4 h-4"})]})]})]}),e.jsxs("div",{className:"bg-white/60 backdrop-blur-sm rounded-2xl p-8 shadow-lg",children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"今日诗句"}),e.jsx("blockquote",{className:"text-lg text-gray-700 italic leading-relaxed",children:'"回忆太美所以人才念旧，物是人非谁不是流泪的旁观者"'}),e.jsx("p",{className:"text-gray-500 mt-2",children:"— 梦的出口"})]})]})}):e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center",children:e.jsxs("div",{className:"text-center max-w-4xl mx-auto px-4",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("div",{className:"w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6",children:e.jsx("span",{className:"text-white font-bold text-2xl",children:"梦"})}),e.jsx("h1",{className:"text-5xl font-bold text-gray-900 mb-4",children:"梦的出口"}),e.jsx("p",{className:"text-xl text-gray-600 mb-8",children:"一个诗意的回忆与情感分享空间"})]}),e.jsxs("div",{className:"grid md:grid-cols-2 gap-8 mb-12",children:[e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg",children:[e.jsx(l,{className:"w-12 h-12 text-blue-500 mx-auto mb-4"}),e.jsx("h3",{className:"text-2xl font-semibold text-gray-900 mb-3",children:"时光书简"}),e.jsx("p",{className:"text-gray-600 leading-relaxed",children:'记录那些珍贵但已逝去的瞬间，创建属于自己的"海市蜃楼"。 每一篇记录都可以包含文字、图片，并可选配一首背景音乐。 回忆太美，所以人才念旧。'})]}),e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg",children:[e.jsx(r,{className:"w-12 h-12 text-purple-500 mx-auto mb-4"}),e.jsx("h3",{className:"text-2xl font-semibold text-gray-900 mb-3",children:"回音长廊"}),e.jsx("p",{className:"text-gray-600 leading-relaxed",children:'匿名分享内心的思绪与情感，如"漫天蝶游"般自由表达。 这些信息在24小时后会自动消失，象征着情感的流逝。 谁不是流泪的旁观者，在无声的共鸣中获得慰藉。'})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(s,{to:"/auth",className:"inline-block bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:from-blue-600 hover:to-purple-700 transition-all transform hover:scale-105 shadow-lg",children:"踏入梦境"}),e.jsx("p",{className:"text-gray-500 text-sm",children:"开始你的诗意之旅"})]})]})})});export{f as default,N as meta};
