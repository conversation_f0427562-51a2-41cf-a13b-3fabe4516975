import{w as j,a as g,o as e,t as a}from"./chunk-QMGIS6GS-Dm1jN8z9.js";import{L as f,B as b,c as N}from"./Layout-B-k2_PuX.js";import{u as v,L as w,E as y}from"./Toast-DTYeq3xB.js";import{u as k,M as E}from"./useChronoEntries-DsUNOhE7.js";import{c as d}from"./user-BOgs6E2m.js";import{C as M,S as _}from"./square-pen-BTbZEIlQ.js";import{f as C}from"./format-CBpsKyOP.js";import{I as L}from"./image-CJpBvlEq.js";import{E as I}from"./eye-BYrV7Kom.js";import"./AuthContext-C3ETFuYa.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],o=d("plus",S);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const P=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],D=d("trash-2",P),F=j(function(){const{entries:t,loading:r,error:l,deleteEntry:x}=k(),[i,n]=g.useState(null),{success:m,error:h,ToastContainer:p}=v(),u=async s=>{if(!confirm("确定要删除这条记录吗？此操作无法撤销。"))return;n(s);const{error:c}=await x(s);c?h("删除失败："+c):m("记录已删除"),n(null)};return e.jsxs(f,{children:[e.jsx(p,{}),e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsxs("div",{className:"flex items-center justify-between mb-8",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold gradient-text mb-2",children:"时光书简"}),e.jsx("p",{className:"text-gray-600",children:"记录珍贵回忆，创建专属的情感档案"})]}),e.jsxs(a,{to:"/journal/new",className:"flex items-center space-x-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-all transform hover:scale-105 shadow-lg btn-ripple",children:[e.jsx(o,{className:"w-5 h-5"}),e.jsx("span",{children:"新建记录"})]})]}),r&&e.jsx("div",{className:"flex items-center justify-center py-12",children:e.jsx(w,{text:"加载记录中..."})}),l&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6 backdrop-blur-enhanced",children:l}),!r&&!l&&t.length===0&&e.jsx(y,{icon:b,title:"还没有任何记录",description:"开始记录你的第一个珍贵回忆吧",action:e.jsxs(a,{to:"/journal/new",className:"inline-flex items-center space-x-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-all transform hover:scale-105 btn-ripple",children:[e.jsx(o,{className:"w-5 h-5"}),e.jsx("span",{children:"创建第一条记录"})]})}),!r&&t.length>0&&e.jsx("div",{className:"space-y-6",children:t.map(s=>e.jsxs("div",{className:"bg-white/80 backdrop-blur-enhanced rounded-2xl p-6 shadow-lg hover-lift",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:s.title}),e.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(M,{className:"w-4 h-4"}),e.jsx("span",{children:C(new Date(s.created_at),"yyyy年MM月dd日")})]}),s.image_url&&e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(L,{className:"w-4 h-4"}),e.jsx("span",{children:"包含图片"})]}),s.music_url&&e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(E,{className:"w-4 h-4"}),e.jsx("span",{children:"包含音乐"})]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(a,{to:`/journal/${s.id}`,className:"p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"查看详情",children:e.jsx(I,{className:"w-5 h-5"})}),e.jsx(a,{to:`/journal/${s.id}/edit`,className:"p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors",title:"编辑",children:e.jsx(_,{className:"w-5 h-5"})}),e.jsx("button",{onClick:()=>u(s.id),disabled:i===s.id,className:N("p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors",i===s.id&&"opacity-50 cursor-not-allowed"),title:"删除",children:e.jsx(D,{className:"w-5 h-5"})})]})]}),e.jsx("p",{className:"text-gray-700 leading-relaxed line-clamp-3",children:s.content}),s.image_url&&e.jsx("div",{className:"mt-4",children:e.jsx("img",{src:s.image_url,alt:"记录配图",className:"w-full h-48 object-cover rounded-lg"})})]},s.id))})]})]})});export{F as default};
