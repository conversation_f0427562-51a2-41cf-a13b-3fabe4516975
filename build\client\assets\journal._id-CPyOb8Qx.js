import{w as b,v as w,a as t,o as e,N as v,t as u}from"./chunk-QMGIS6GS-Dm1jN8z9.js";import{L as _}from"./Layout-B-k2_PuX.js";import{P as E}from"./ProtectedRoute-BQ7DlOLV.js";import{u as P,M as k}from"./useChronoEntries-DsUNOhE7.js";import"./AuthContext-C3ETFuYa.js";import{c}from"./user-BOgs6E2m.js";import{S as L,C as M}from"./square-pen-BTbZEIlQ.js";import{f as h}from"./format-CBpsKyOP.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S=[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]],C=c("arrow-left",S);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const H=[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]],A=c("pause",H);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $=[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]],z=c("play",$),O=b(function(){const{id:n}=w(),{getEntry:o}=P(),[s,p]=t.useState(null),[f,d]=t.useState(!0),[x,j]=t.useState(null),[i,r]=t.useState(!1),[a,y]=t.useState(null);t.useEffect(()=>{(async()=>{if(!n)return;d(!0);const{data:N,error:m}=await o(n);m?j(m):p(N),d(!1)})()},[n,o]),t.useEffect(()=>()=>{a&&(a.pause(),a.src="")},[a]);const g=()=>{if(s!=null&&s.music_url)if(a)i?(a.pause(),r(!1)):(a.play(),r(!0));else{const l=new Audio(s.music_url);l.addEventListener("ended",()=>r(!1)),l.addEventListener("error",()=>{r(!1),alert("音乐播放失败")}),y(l),l.play(),r(!0)}};return n?e.jsx(E,{children:e.jsx(_,{children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsx("div",{className:"mb-6",children:e.jsxs(u,{to:"/journal",className:"inline-flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors",children:[e.jsx(C,{className:"w-5 h-5"}),e.jsx("span",{children:"返回书简列表"})]})}),f&&e.jsx("div",{className:"flex items-center justify-center py-12",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}),x&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6",children:x}),s&&e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl overflow-hidden",children:[e.jsxs("div",{className:"p-8 border-b border-gray-200",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:s.title}),e.jsxs(u,{to:`/journal/${s.id}/edit`,className:"flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:[e.jsx(L,{className:"w-4 h-4"}),e.jsx("span",{children:"编辑"})]})]}),e.jsxs("div",{className:"flex items-center space-x-6 text-gray-600",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(M,{className:"w-5 h-5"}),e.jsxs("span",{children:["创建于 ",h(new Date(s.created_at),"yyyy年MM月dd日 HH:mm")]})]}),s.updated_at!==s.created_at&&e.jsxs("div",{className:"text-sm",children:["最后更新：",h(new Date(s.updated_at),"yyyy年MM月dd日 HH:mm")]})]})]}),e.jsxs("div",{className:"p-8",children:[s.image_url&&e.jsx("div",{className:"mb-8",children:e.jsx("img",{src:s.image_url,alt:"记录配图",className:"w-full max-h-96 object-cover rounded-lg shadow-lg"})}),s.music_url&&e.jsx("div",{className:"mb-8 p-4 bg-gray-50 rounded-lg",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("button",{onClick:g,className:"flex items-center justify-center w-12 h-12 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors",children:i?e.jsx(A,{className:"w-6 h-6"}):e.jsx(z,{className:"w-6 h-6"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(k,{className:"w-5 h-5 text-gray-600"}),e.jsx("span",{className:"text-gray-700",children:"背景音乐"})]}),e.jsx("div",{className:"text-sm text-gray-500",children:i?"正在播放...":"点击播放"})]})]})}),e.jsx("div",{className:"prose prose-lg max-w-none",children:e.jsx("div",{className:"text-gray-800 leading-relaxed whitespace-pre-wrap",children:s.content})})]})]})]})})}):e.jsx(v,{to:"/journal",replace:!0})});export{O as default};
