import{w as a,p as i,o as s,M as c,L as l,S as p,q as h,O as u,s as d}from"./chunk-QMGIS6GS-Dm1jN8z9.js";import{A as x}from"./AuthContext-C3ETFuYa.js";const j=()=>[{rel:"preconnect",href:"https://fonts.googleapis.com"},{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"},{rel:"stylesheet",href:"https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"}];function g({children:e}){return s.jsxs("html",{lang:"zh-CN",children:[s.jsxs("head",{children:[s.jsx("meta",{charSet:"utf-8"}),s.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),s.jsx(c,{}),s.jsx(l,{})]}),s.jsxs("body",{children:[e,s.jsx(p,{}),s.jsx(h,{})]})]})}const y=a(function(){return s.jsx(x,{children:s.jsx(u,{})})}),w=i(function({error:t}){let r="Oops!",o="An unexpected error occurred.",n;return d(t)&&(r=t.status===404?"404":"Error",o=t.status===404?"The requested page could not be found.":t.statusText||o),s.jsxs("main",{className:"pt-16 p-4 container mx-auto",children:[s.jsx("h1",{children:r}),s.jsx("p",{children:o}),n]})});export{w as ErrorBoundary,g as Layout,y as default,j as links};
