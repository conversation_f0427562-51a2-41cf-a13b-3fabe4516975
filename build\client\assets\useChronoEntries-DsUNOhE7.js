import{c as w}from"./user-BOgs6E2m.js";import{a as i}from"./chunk-QMGIS6GS-Dm1jN8z9.js";import{u as q,s as n}from"./AuthContext-C3ETFuYa.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M=[["path",{d:"M9 18V5l12-2v13",key:"1jmyc2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["circle",{cx:"18",cy:"16",r:"3",key:"1hluhg"}]],C=w("music",M);function L(){const[d,a]=i.useState([]),[h,u]=i.useState(!0),[m,c]=i.useState(null),{user:o}=q(),f=async()=>{if(o)try{u(!0);const{data:t,error:r}=await n.from("chrono_entries").select("*").eq("user_id",o.id).order("created_at",{ascending:!1});if(r)throw r;a(t||[])}catch(t){c(t instanceof Error?t.message:"获取记录失败")}finally{u(!1)}},y=async t=>{if(!o)return{error:"用户未登录"};try{const{data:r,error:e}=await n.from("chrono_entries").insert([{...t,user_id:o.id}]).select().single();if(e)throw e;return a(s=>[r,...s]),{data:r,error:null}}catch(r){const e=r instanceof Error?r.message:"创建记录失败";return c(e),{error:e}}},g=async(t,r)=>{if(!o)return{error:"用户未登录"};try{const{data:e,error:s}=await n.from("chrono_entries").update(r).eq("id",t).eq("user_id",o.id).select().single();if(s)throw s;return a(p=>p.map(l=>l.id===t?e:l)),{data:e,error:null}}catch(e){const s=e instanceof Error?e.message:"更新记录失败";return c(s),{error:s}}},E=async t=>{if(!o)return{error:"用户未登录"};try{const{error:r}=await n.from("chrono_entries").delete().eq("id",t).eq("user_id",o.id);if(r)throw r;return a(e=>e.filter(s=>s.id!==t)),{error:null}}catch(r){const e=r instanceof Error?r.message:"删除记录失败";return c(e),{error:e}}},_=async t=>{if(!o)return{data:null,error:"用户未登录"};try{const{data:r,error:e}=await n.from("chrono_entries").select("*").eq("id",t).eq("user_id",o.id).single();if(e)throw e;return{data:r,error:null}}catch(r){return{data:null,error:r instanceof Error?r.message:"获取记录失败"}}};return i.useEffect(()=>{f()},[o]),{entries:d,loading:h,error:m,createEntry:y,updateEntry:g,deleteEntry:E,getEntry:_,refetch:f}}export{C as M,L as u};
