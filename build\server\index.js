import { jsx, jsxs, Fragment } from "react/jsx-runtime";
import { PassThrough } from "node:stream";
import { createReadableStreamFromReadable } from "@react-router/node";
import { ServerRouter, UNSAFE_withComponentProps, Outlet, UNSAFE_withErrorBoundaryProps, isRouteErrorResponse, Meta, Links, ScrollRestoration, Scripts, useLocation, Link, Navigate, useNavigate, useParams } from "react-router";
import { isbot } from "isbot";
import { renderToPipeableStream } from "react-dom/server";
import { useState, useEffect, createContext, useContext } from "react";
import { createClient } from "@supabase/supabase-js";
import { BookOpen, MessageCircle, User, LogOut, X, Menu, Heart, Star, Mail, Lock, EyeOff, Eye, AlertCircle, XCircle, CheckCircle, Plus, Calendar, Image, Music, Edit, Trash2, Save, ArrowLeft, Pause, Play, Clock, Send, Sparkles, RefreshCw } from "lucide-react";
import clsx from "clsx";
import { format } from "date-fns";
const streamTimeout = 5e3;
function handleRequest(request, responseStatusCode, responseHeaders, routerContext, loadContext) {
  return new Promise((resolve, reject) => {
    let shellRendered = false;
    let userAgent = request.headers.get("user-agent");
    let readyOption = userAgent && isbot(userAgent) || routerContext.isSpaMode ? "onAllReady" : "onShellReady";
    const { pipe, abort } = renderToPipeableStream(
      /* @__PURE__ */ jsx(ServerRouter, { context: routerContext, url: request.url }),
      {
        [readyOption]() {
          shellRendered = true;
          const body = new PassThrough();
          const stream = createReadableStreamFromReadable(body);
          responseHeaders.set("Content-Type", "text/html");
          resolve(
            new Response(stream, {
              headers: responseHeaders,
              status: responseStatusCode
            })
          );
          pipe(body);
        },
        onShellError(error) {
          reject(error);
        },
        onError(error) {
          responseStatusCode = 500;
          if (shellRendered) {
            console.error(error);
          }
        }
      }
    );
    setTimeout(abort, streamTimeout + 1e3);
  });
}
const entryServer = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: handleRequest,
  streamTimeout
}, Symbol.toStringTag, { value: "Module" }));
const supabaseUrl = "https://hdpvcljypnlnebpcvism.supabase.co";
const supabaseAnonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhkcHZjbGp5cG5sbmVicGN2aXNtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1NTcxNTksImV4cCI6MjA2ODEzMzE1OX0.l9oDolH9iyrUXJ36SVefpx8kT3kAOO1twFZUuDmMYys";
const supabase = createClient(supabaseUrl, supabaseAnonKey);
function useAuth() {
  const [user, setUser] = useState(null);
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session: session2 }, error: error2 }) => {
      if (error2) {
        console.error("获取会话失败:", error2);
        setError(error2.message);
      }
      setSession(session2);
      setUser((session2 == null ? void 0 : session2.user) ?? null);
      setLoading(false);
    }).catch((err) => {
      console.error("身份验证初始化失败:", err);
      setError("身份验证服务不可用");
      setLoading(false);
    });
    const {
      data: { subscription }
    } = supabase.auth.onAuthStateChange((_event, session2) => {
      setSession(session2);
      setUser((session2 == null ? void 0 : session2.user) ?? null);
      setError(null);
      setLoading(false);
    });
    return () => subscription.unsubscribe();
  }, []);
  const signUp = async (email, password) => {
    const { data, error: error2 } = await supabase.auth.signUp({
      email,
      password
    });
    return { data, error: error2 };
  };
  const signIn = async (email, password) => {
    const { data, error: error2 } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    return { data, error: error2 };
  };
  const signOut = async () => {
    const { error: error2 } = await supabase.auth.signOut();
    return { error: error2 };
  };
  return {
    user,
    session,
    loading,
    error,
    signUp,
    signIn,
    signOut
  };
}
const AuthContext = createContext(void 0);
function AuthProvider({ children }) {
  const auth2 = useAuth();
  return /* @__PURE__ */ jsx(AuthContext.Provider, { value: auth2, children });
}
function useAuthContext() {
  const context = useContext(AuthContext);
  if (context === void 0) {
    throw new Error("useAuthContext must be used within an AuthProvider");
  }
  return context;
}
const links = () => [{
  rel: "preconnect",
  href: "https://fonts.googleapis.com"
}, {
  rel: "preconnect",
  href: "https://fonts.gstatic.com",
  crossOrigin: "anonymous"
}, {
  rel: "stylesheet",
  href: "https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
}];
function Layout$1({
  children
}) {
  return /* @__PURE__ */ jsxs("html", {
    lang: "zh-CN",
    children: [/* @__PURE__ */ jsxs("head", {
      children: [/* @__PURE__ */ jsx("meta", {
        charSet: "utf-8"
      }), /* @__PURE__ */ jsx("meta", {
        name: "viewport",
        content: "width=device-width, initial-scale=1"
      }), /* @__PURE__ */ jsx(Meta, {}), /* @__PURE__ */ jsx(Links, {})]
    }), /* @__PURE__ */ jsxs("body", {
      children: [children, /* @__PURE__ */ jsx(ScrollRestoration, {}), /* @__PURE__ */ jsx(Scripts, {})]
    })]
  });
}
const root = UNSAFE_withComponentProps(function App() {
  return /* @__PURE__ */ jsx(AuthProvider, {
    children: /* @__PURE__ */ jsx(Outlet, {})
  });
});
const ErrorBoundary = UNSAFE_withErrorBoundaryProps(function ErrorBoundary2({
  error
}) {
  let message = "Oops!";
  let details = "An unexpected error occurred.";
  let stack;
  if (isRouteErrorResponse(error)) {
    message = error.status === 404 ? "404" : "Error";
    details = error.status === 404 ? "The requested page could not be found." : error.statusText || details;
  }
  return /* @__PURE__ */ jsxs("main", {
    className: "pt-16 p-4 container mx-auto",
    children: [/* @__PURE__ */ jsx("h1", {
      children: message
    }), /* @__PURE__ */ jsx("p", {
      children: details
    }), stack]
  });
});
const route0 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  ErrorBoundary,
  Layout: Layout$1,
  default: root,
  links
}, Symbol.toStringTag, { value: "Module" }));
function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { user, signOut } = useAuthContext();
  const location = useLocation();
  const handleSignOut = async () => {
    await signOut();
  };
  const navItems = [
    {
      name: "时光书简",
      href: "/journal",
      icon: BookOpen,
      description: "记录珍贵回忆"
    },
    {
      name: "回音长廊",
      href: "/echoes",
      icon: MessageCircle,
      description: "匿名情感分享"
    }
  ];
  return /* @__PURE__ */ jsx("nav", { className: "bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50", children: /* @__PURE__ */ jsxs("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: [
    /* @__PURE__ */ jsxs("div", { className: "flex justify-between items-center h-16", children: [
      /* @__PURE__ */ jsxs(Link, { to: "/", className: "flex items-center space-x-2", children: [
        /* @__PURE__ */ jsx("div", { className: "w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center", children: /* @__PURE__ */ jsx("span", { className: "text-white font-bold text-sm", children: "梦" }) }),
        /* @__PURE__ */ jsx("span", { className: "text-xl font-bold text-gray-900", children: "梦的出口" })
      ] }),
      /* @__PURE__ */ jsx("div", { className: "hidden md:flex items-center space-x-8", children: navItems.map((item) => {
        const Icon = item.icon;
        const isActive = location.pathname.startsWith(item.href);
        return /* @__PURE__ */ jsxs(
          Link,
          {
            to: item.href,
            className: clsx(
              "flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors",
              isActive ? "bg-blue-50 text-blue-700" : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
            ),
            children: [
              /* @__PURE__ */ jsx(Icon, { className: "w-5 h-5" }),
              /* @__PURE__ */ jsx("span", { className: "font-medium", children: item.name })
            ]
          },
          item.href
        );
      }) }),
      /* @__PURE__ */ jsx("div", { className: "hidden md:flex items-center space-x-4", children: user && /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-3", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-2 text-gray-700", children: [
          /* @__PURE__ */ jsx(User, { className: "w-5 h-5" }),
          /* @__PURE__ */ jsx("span", { className: "text-sm", children: user.email })
        ] }),
        /* @__PURE__ */ jsxs(
          "button",
          {
            onClick: handleSignOut,
            className: "flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors",
            children: [
              /* @__PURE__ */ jsx(LogOut, { className: "w-5 h-5" }),
              /* @__PURE__ */ jsx("span", { children: "登出" })
            ]
          }
        )
      ] }) }),
      /* @__PURE__ */ jsx("div", { className: "md:hidden", children: /* @__PURE__ */ jsx(
        "button",
        {
          onClick: () => setIsMenuOpen(!isMenuOpen),
          className: "p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-50",
          children: isMenuOpen ? /* @__PURE__ */ jsx(X, { className: "w-6 h-6" }) : /* @__PURE__ */ jsx(Menu, { className: "w-6 h-6" })
        }
      ) })
    ] }),
    isMenuOpen && /* @__PURE__ */ jsx("div", { className: "md:hidden py-4 border-t border-gray-200", children: /* @__PURE__ */ jsxs("div", { className: "space-y-2", children: [
      navItems.map((item) => {
        const Icon = item.icon;
        const isActive = location.pathname.startsWith(item.href);
        return /* @__PURE__ */ jsxs(
          Link,
          {
            to: item.href,
            onClick: () => setIsMenuOpen(false),
            className: clsx(
              "flex items-center space-x-3 px-3 py-3 rounded-lg transition-colors",
              isActive ? "bg-blue-50 text-blue-700" : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
            ),
            children: [
              /* @__PURE__ */ jsx(Icon, { className: "w-5 h-5" }),
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsx("div", { className: "font-medium", children: item.name }),
                /* @__PURE__ */ jsx("div", { className: "text-sm text-gray-500", children: item.description })
              ] })
            ]
          },
          item.href
        );
      }),
      user && /* @__PURE__ */ jsxs("div", { className: "pt-4 border-t border-gray-200", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-3 px-3 py-2 text-gray-700", children: [
          /* @__PURE__ */ jsx(User, { className: "w-5 h-5" }),
          /* @__PURE__ */ jsx("span", { className: "text-sm", children: user.email })
        ] }),
        /* @__PURE__ */ jsxs(
          "button",
          {
            onClick: handleSignOut,
            className: "w-full flex items-center space-x-3 px-3 py-3 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors",
            children: [
              /* @__PURE__ */ jsx(LogOut, { className: "w-5 h-5" }),
              /* @__PURE__ */ jsx("span", { children: "登出" })
            ]
          }
        )
      ] })
    ] }) })
  ] }) });
}
function Layout({ children }) {
  return /* @__PURE__ */ jsxs("div", { className: "min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50", children: [
    /* @__PURE__ */ jsx(Navbar, {}),
    /* @__PURE__ */ jsx("main", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8", children })
  ] });
}
function meta({}) {
  return [{
    title: "梦的出口 - 诗意的回忆与情感分享空间"
  }, {
    name: "description",
    content: "在这里记录珍贵回忆，分享内心情感，寻找心灵的共鸣"
  }];
}
const home = UNSAFE_withComponentProps(function Home() {
  var _a;
  const {
    user
  } = useAuthContext();
  if (!user) {
    return /* @__PURE__ */ jsx("div", {
      className: "min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center",
      children: /* @__PURE__ */ jsxs("div", {
        className: "text-center max-w-4xl mx-auto px-4",
        children: [/* @__PURE__ */ jsxs("div", {
          className: "mb-8",
          children: [/* @__PURE__ */ jsx("div", {
            className: "w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6",
            children: /* @__PURE__ */ jsx("span", {
              className: "text-white font-bold text-2xl",
              children: "梦"
            })
          }), /* @__PURE__ */ jsx("h1", {
            className: "text-5xl font-bold text-gray-900 mb-4",
            children: "梦的出口"
          }), /* @__PURE__ */ jsx("p", {
            className: "text-xl text-gray-600 mb-8",
            children: "一个诗意的回忆与情感分享空间"
          })]
        }), /* @__PURE__ */ jsxs("div", {
          className: "grid md:grid-cols-2 gap-8 mb-12",
          children: [/* @__PURE__ */ jsxs("div", {
            className: "bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg",
            children: [/* @__PURE__ */ jsx(BookOpen, {
              className: "w-12 h-12 text-blue-500 mx-auto mb-4"
            }), /* @__PURE__ */ jsx("h3", {
              className: "text-2xl font-semibold text-gray-900 mb-3",
              children: "时光书简"
            }), /* @__PURE__ */ jsx("p", {
              className: "text-gray-600 leading-relaxed",
              children: '记录那些珍贵但已逝去的瞬间，创建属于自己的"海市蜃楼"。 每一篇记录都可以包含文字、图片，并可选配一首背景音乐。 回忆太美，所以人才念旧。'
            })]
          }), /* @__PURE__ */ jsxs("div", {
            className: "bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg",
            children: [/* @__PURE__ */ jsx(MessageCircle, {
              className: "w-12 h-12 text-purple-500 mx-auto mb-4"
            }), /* @__PURE__ */ jsx("h3", {
              className: "text-2xl font-semibold text-gray-900 mb-3",
              children: "回音长廊"
            }), /* @__PURE__ */ jsx("p", {
              className: "text-gray-600 leading-relaxed",
              children: '匿名分享内心的思绪与情感，如"漫天蝶游"般自由表达。 这些信息在24小时后会自动消失，象征着情感的流逝。 谁不是流泪的旁观者，在无声的共鸣中获得慰藉。'
            })]
          })]
        }), /* @__PURE__ */ jsxs("div", {
          className: "space-y-4",
          children: [/* @__PURE__ */ jsx(Link, {
            to: "/auth",
            className: "inline-block bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:from-blue-600 hover:to-purple-700 transition-all transform hover:scale-105 shadow-lg",
            children: "踏入梦境"
          }), /* @__PURE__ */ jsx("p", {
            className: "text-gray-500 text-sm",
            children: "开始你的诗意之旅"
          })]
        })]
      })
    });
  }
  return /* @__PURE__ */ jsx(Layout, {
    children: /* @__PURE__ */ jsxs("div", {
      className: "text-center max-w-4xl mx-auto",
      children: [/* @__PURE__ */ jsxs("div", {
        className: "mb-12",
        children: [/* @__PURE__ */ jsxs("h1", {
          className: "text-4xl font-bold text-gray-900 mb-4",
          children: ["欢迎回到梦境，", (_a = user.email) == null ? void 0 : _a.split("@")[0]]
        }), /* @__PURE__ */ jsx("p", {
          className: "text-xl text-gray-600",
          children: "在这里记录美好，分享情感，寻找心灵的共鸣"
        })]
      }), /* @__PURE__ */ jsxs("div", {
        className: "grid md:grid-cols-2 gap-8 mb-12",
        children: [/* @__PURE__ */ jsxs(Link, {
          to: "/journal",
          className: "group bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all transform hover:scale-105",
          children: [/* @__PURE__ */ jsx(BookOpen, {
            className: "w-16 h-16 text-blue-500 mx-auto mb-6 group-hover:scale-110 transition-transform"
          }), /* @__PURE__ */ jsx("h3", {
            className: "text-2xl font-semibold text-gray-900 mb-3",
            children: "时光书简"
          }), /* @__PURE__ */ jsx("p", {
            className: "text-gray-600 mb-4",
            children: "记录珍贵回忆，创建专属的情感档案"
          }), /* @__PURE__ */ jsxs("div", {
            className: "flex items-center justify-center space-x-2 text-blue-500",
            children: [/* @__PURE__ */ jsx("span", {
              children: "开始记录"
            }), /* @__PURE__ */ jsx(Heart, {
              className: "w-4 h-4"
            })]
          })]
        }), /* @__PURE__ */ jsxs(Link, {
          to: "/echoes",
          className: "group bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all transform hover:scale-105",
          children: [/* @__PURE__ */ jsx(MessageCircle, {
            className: "w-16 h-16 text-purple-500 mx-auto mb-6 group-hover:scale-110 transition-transform"
          }), /* @__PURE__ */ jsx("h3", {
            className: "text-2xl font-semibold text-gray-900 mb-3",
            children: "回音长廊"
          }), /* @__PURE__ */ jsx("p", {
            className: "text-gray-600 mb-4",
            children: "匿名分享情感，在共鸣中找到慰藉"
          }), /* @__PURE__ */ jsxs("div", {
            className: "flex items-center justify-center space-x-2 text-purple-500",
            children: [/* @__PURE__ */ jsx("span", {
              children: "探索长廊"
            }), /* @__PURE__ */ jsx(Star, {
              className: "w-4 h-4"
            })]
          })]
        })]
      }), /* @__PURE__ */ jsxs("div", {
        className: "bg-white/60 backdrop-blur-sm rounded-2xl p-8 shadow-lg",
        children: [/* @__PURE__ */ jsx("h3", {
          className: "text-xl font-semibold text-gray-900 mb-4",
          children: "今日诗句"
        }), /* @__PURE__ */ jsx("blockquote", {
          className: "text-lg text-gray-700 italic leading-relaxed",
          children: '"回忆太美所以人才念旧，物是人非谁不是流泪的旁观者"'
        }), /* @__PURE__ */ jsx("p", {
          className: "text-gray-500 mt-2",
          children: "— 梦的出口"
        })]
      })]
    })
  });
});
const route1 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: home,
  meta
}, Symbol.toStringTag, { value: "Module" }));
function LoginForm({ onToggleMode }) {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const { signIn } = useAuthContext();
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");
    const { error: error2 } = await signIn(email, password);
    if (error2) {
      setError(error2.message);
    }
    setLoading(false);
  };
  return /* @__PURE__ */ jsxs("div", { className: "w-full max-w-md mx-auto", children: [
    /* @__PURE__ */ jsxs("div", { className: "text-center mb-8", children: [
      /* @__PURE__ */ jsx("h1", { className: "text-3xl font-bold text-gray-900 mb-2", children: "欢迎回到梦境" }),
      /* @__PURE__ */ jsx("p", { className: "text-gray-600", children: "登录你的梦的出口" })
    ] }),
    /* @__PURE__ */ jsxs("form", { onSubmit: handleSubmit, className: "space-y-6", children: [
      /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsx("label", { htmlFor: "email", className: "block text-sm font-medium text-gray-700 mb-2", children: "邮箱地址" }),
        /* @__PURE__ */ jsxs("div", { className: "relative", children: [
          /* @__PURE__ */ jsx(Mail, { className: "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" }),
          /* @__PURE__ */ jsx(
            "input",
            {
              id: "email",
              type: "email",
              value: email,
              onChange: (e) => setEmail(e.target.value),
              className: "w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",
              placeholder: "请输入邮箱地址",
              required: true
            }
          )
        ] })
      ] }),
      /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsx("label", { htmlFor: "password", className: "block text-sm font-medium text-gray-700 mb-2", children: "密码" }),
        /* @__PURE__ */ jsxs("div", { className: "relative", children: [
          /* @__PURE__ */ jsx(Lock, { className: "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" }),
          /* @__PURE__ */ jsx(
            "input",
            {
              id: "password",
              type: showPassword ? "text" : "password",
              value: password,
              onChange: (e) => setPassword(e.target.value),
              className: "w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",
              placeholder: "请输入密码",
              required: true
            }
          ),
          /* @__PURE__ */ jsx(
            "button",
            {
              type: "button",
              onClick: () => setShowPassword(!showPassword),
              className: "absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",
              children: showPassword ? /* @__PURE__ */ jsx(EyeOff, { className: "w-5 h-5" }) : /* @__PURE__ */ jsx(Eye, { className: "w-5 h-5" })
            }
          )
        ] })
      ] }),
      error && /* @__PURE__ */ jsx("div", { className: "bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg", children: error }),
      /* @__PURE__ */ jsx(
        "button",
        {
          type: "submit",
          disabled: loading,
          className: "w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",
          children: loading ? "登录中..." : "登录"
        }
      )
    ] }),
    /* @__PURE__ */ jsx("div", { className: "mt-6 text-center", children: /* @__PURE__ */ jsxs("p", { className: "text-gray-600", children: [
      "还没有账户？",
      " ",
      /* @__PURE__ */ jsx(
        "button",
        {
          onClick: onToggleMode,
          className: "text-blue-600 hover:text-blue-700 font-medium",
          children: "立即注册"
        }
      )
    ] }) })
  ] });
}
function SignUpForm({ onToggleMode }) {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);
  const { signUp } = useAuthContext();
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");
    if (password !== confirmPassword) {
      setError("密码确认不匹配");
      setLoading(false);
      return;
    }
    if (password.length < 6) {
      setError("密码长度至少为6位");
      setLoading(false);
      return;
    }
    const { error: error2 } = await signUp(email, password);
    if (error2) {
      setError(error2.message);
    } else {
      setSuccess(true);
    }
    setLoading(false);
  };
  if (success) {
    return /* @__PURE__ */ jsx("div", { className: "w-full max-w-md mx-auto text-center", children: /* @__PURE__ */ jsxs("div", { className: "bg-green-50 border border-green-200 text-green-700 px-6 py-8 rounded-lg", children: [
      /* @__PURE__ */ jsx(User, { className: "w-16 h-16 mx-auto mb-4 text-green-500" }),
      /* @__PURE__ */ jsx("h2", { className: "text-xl font-semibold mb-2", children: "注册成功！" }),
      /* @__PURE__ */ jsx("p", { className: "mb-4", children: "我们已向您的邮箱发送了确认邮件，请点击邮件中的链接来激活您的账户。" }),
      /* @__PURE__ */ jsx(
        "button",
        {
          onClick: onToggleMode,
          className: "text-blue-600 hover:text-blue-700 font-medium",
          children: "返回登录"
        }
      )
    ] }) });
  }
  return /* @__PURE__ */ jsxs("div", { className: "w-full max-w-md mx-auto", children: [
    /* @__PURE__ */ jsxs("div", { className: "text-center mb-8", children: [
      /* @__PURE__ */ jsx("h1", { className: "text-3xl font-bold text-gray-900 mb-2", children: "踏入梦境" }),
      /* @__PURE__ */ jsx("p", { className: "text-gray-600", children: "创建你的梦的出口账户" })
    ] }),
    /* @__PURE__ */ jsxs("form", { onSubmit: handleSubmit, className: "space-y-6", children: [
      /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsx("label", { htmlFor: "email", className: "block text-sm font-medium text-gray-700 mb-2", children: "邮箱地址" }),
        /* @__PURE__ */ jsxs("div", { className: "relative", children: [
          /* @__PURE__ */ jsx(Mail, { className: "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" }),
          /* @__PURE__ */ jsx(
            "input",
            {
              id: "email",
              type: "email",
              value: email,
              onChange: (e) => setEmail(e.target.value),
              className: "w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",
              placeholder: "请输入邮箱地址",
              required: true
            }
          )
        ] })
      ] }),
      /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsx("label", { htmlFor: "password", className: "block text-sm font-medium text-gray-700 mb-2", children: "密码" }),
        /* @__PURE__ */ jsxs("div", { className: "relative", children: [
          /* @__PURE__ */ jsx(Lock, { className: "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" }),
          /* @__PURE__ */ jsx(
            "input",
            {
              id: "password",
              type: showPassword ? "text" : "password",
              value: password,
              onChange: (e) => setPassword(e.target.value),
              className: "w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",
              placeholder: "请输入密码（至少6位）",
              required: true
            }
          ),
          /* @__PURE__ */ jsx(
            "button",
            {
              type: "button",
              onClick: () => setShowPassword(!showPassword),
              className: "absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",
              children: showPassword ? /* @__PURE__ */ jsx(EyeOff, { className: "w-5 h-5" }) : /* @__PURE__ */ jsx(Eye, { className: "w-5 h-5" })
            }
          )
        ] })
      ] }),
      /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsx("label", { htmlFor: "confirmPassword", className: "block text-sm font-medium text-gray-700 mb-2", children: "确认密码" }),
        /* @__PURE__ */ jsxs("div", { className: "relative", children: [
          /* @__PURE__ */ jsx(Lock, { className: "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" }),
          /* @__PURE__ */ jsx(
            "input",
            {
              id: "confirmPassword",
              type: showPassword ? "text" : "password",
              value: confirmPassword,
              onChange: (e) => setConfirmPassword(e.target.value),
              className: "w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",
              placeholder: "请再次输入密码",
              required: true
            }
          )
        ] })
      ] }),
      error && /* @__PURE__ */ jsx("div", { className: "bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg", children: error }),
      /* @__PURE__ */ jsx(
        "button",
        {
          type: "submit",
          disabled: loading,
          className: "w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",
          children: loading ? "注册中..." : "注册"
        }
      )
    ] }),
    /* @__PURE__ */ jsx("div", { className: "mt-6 text-center", children: /* @__PURE__ */ jsxs("p", { className: "text-gray-600", children: [
      "已有账户？",
      " ",
      /* @__PURE__ */ jsx(
        "button",
        {
          onClick: onToggleMode,
          className: "text-blue-600 hover:text-blue-700 font-medium",
          children: "立即登录"
        }
      )
    ] }) })
  ] });
}
const auth = UNSAFE_withComponentProps(function AuthPage() {
  const [isLogin, setIsLogin] = useState(true);
  const {
    user,
    loading
  } = useAuthContext();
  if (loading) {
    return /* @__PURE__ */ jsx("div", {
      className: "min-h-screen flex items-center justify-center",
      children: /* @__PURE__ */ jsx("div", {
        className: "animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"
      })
    });
  }
  if (user) {
    return /* @__PURE__ */ jsx(Navigate, {
      to: "/",
      replace: true
    });
  }
  return /* @__PURE__ */ jsx("div", {
    className: "min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4",
    children: /* @__PURE__ */ jsxs("div", {
      className: "w-full max-w-md",
      children: [/* @__PURE__ */ jsx("div", {
        className: "bg-white rounded-2xl shadow-xl p-8",
        children: isLogin ? /* @__PURE__ */ jsx(LoginForm, {
          onToggleMode: () => setIsLogin(false)
        }) : /* @__PURE__ */ jsx(SignUpForm, {
          onToggleMode: () => setIsLogin(true)
        })
      }), /* @__PURE__ */ jsx("div", {
        className: "text-center mt-8 text-gray-500 text-sm",
        children: /* @__PURE__ */ jsx("p", {
          children: "梦的出口 - 一个诗意的回忆与情感分享空间"
        })
      })]
    })
  });
});
const route2 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: auth
}, Symbol.toStringTag, { value: "Module" }));
function LoadingSpinner({
  size = "md",
  color = "blue",
  text
}) {
  const sizeClasses = {
    sm: "w-6 h-6",
    md: "w-12 h-12",
    lg: "w-16 h-16"
  };
  const colorClasses = {
    blue: "border-blue-600",
    purple: "border-purple-600",
    gray: "border-gray-600"
  };
  return /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center justify-center space-y-4", children: [
    /* @__PURE__ */ jsx("div", { className: clsx(
      "animate-spin rounded-full border-b-2",
      sizeClasses[size],
      colorClasses[color]
    ) }),
    text && /* @__PURE__ */ jsx("p", { className: "text-gray-600 text-sm animate-pulse", children: text })
  ] });
}
function EmptyState({
  icon: Icon,
  title,
  description,
  action,
  className = ""
}) {
  return /* @__PURE__ */ jsxs("div", { className: `text-center py-12 ${className}`, children: [
    /* @__PURE__ */ jsx("div", { className: "w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mx-auto mb-6 animate-float", children: /* @__PURE__ */ jsx(Icon, { className: "w-8 h-8 text-gray-400" }) }),
    /* @__PURE__ */ jsx("h3", { className: "text-xl font-semibold text-gray-900 mb-2", children: title }),
    /* @__PURE__ */ jsx("p", { className: "text-gray-600 mb-6 max-w-md mx-auto", children: description }),
    action && action
  ] });
}
function Toast({ type, message, onClose, duration = 5e3 }) {
  const [isVisible, setIsVisible] = useState(true);
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(onClose, 300);
    }, duration);
    return () => clearTimeout(timer);
  }, [duration, onClose]);
  const icons = {
    success: CheckCircle,
    error: XCircle,
    warning: AlertCircle
  };
  const colors = {
    success: "bg-green-50 border-green-200 text-green-800",
    error: "bg-red-50 border-red-200 text-red-800",
    warning: "bg-yellow-50 border-yellow-200 text-yellow-800"
  };
  const iconColors = {
    success: "text-green-500",
    error: "text-red-500",
    warning: "text-yellow-500"
  };
  const Icon = icons[type];
  return /* @__PURE__ */ jsx("div", { className: clsx(
    "fixed top-4 right-4 z-50 max-w-sm w-full transition-all duration-300 transform",
    isVisible ? "translate-x-0 opacity-100" : "translate-x-full opacity-0"
  ), children: /* @__PURE__ */ jsxs("div", { className: clsx(
    "flex items-start space-x-3 p-4 rounded-lg border shadow-lg backdrop-blur-sm",
    colors[type]
  ), children: [
    /* @__PURE__ */ jsx(Icon, { className: clsx("w-5 h-5 mt-0.5 flex-shrink-0", iconColors[type]) }),
    /* @__PURE__ */ jsx("div", { className: "flex-1", children: /* @__PURE__ */ jsx("p", { className: "text-sm font-medium", children: message }) }),
    /* @__PURE__ */ jsx(
      "button",
      {
        onClick: () => {
          setIsVisible(false);
          setTimeout(onClose, 300);
        },
        className: "flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors",
        children: /* @__PURE__ */ jsx(X, { className: "w-4 h-4" })
      }
    )
  ] }) });
}
function useToast() {
  const [toasts, setToasts] = useState([]);
  const addToast = (type, message) => {
    const id = Math.random().toString(36).substr(2, 9);
    setToasts((prev) => [...prev, { id, type, message }]);
  };
  const removeToast = (id) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id));
  };
  const success = (message) => addToast("success", message);
  const error = (message) => addToast("error", message);
  const warning = (message) => addToast("warning", message);
  const ToastContainer = () => /* @__PURE__ */ jsx("div", { className: "fixed top-4 right-4 z-50 space-y-2", children: toasts.map((toast) => /* @__PURE__ */ jsx(
    Toast,
    {
      type: toast.type,
      message: toast.message,
      onClose: () => removeToast(toast.id)
    },
    toast.id
  )) });
  return {
    success,
    error,
    warning,
    ToastContainer
  };
}
function useChronoEntries() {
  const [entries, setEntries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user } = useAuthContext();
  const fetchEntries = async () => {
    if (!user) return;
    try {
      setLoading(true);
      const { data, error: error2 } = await supabase.from("chrono_entries").select("*").eq("user_id", user.id).order("created_at", { ascending: false });
      if (error2) throw error2;
      setEntries(data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取记录失败");
    } finally {
      setLoading(false);
    }
  };
  const createEntry = async (entry2) => {
    if (!user) return { error: "用户未登录" };
    try {
      const { data, error: error2 } = await supabase.from("chrono_entries").insert([{ ...entry2, user_id: user.id }]).select().single();
      if (error2) throw error2;
      setEntries((prev) => [data, ...prev]);
      return { data, error: null };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "创建记录失败";
      setError(errorMessage);
      return { error: errorMessage };
    }
  };
  const updateEntry = async (id, updates) => {
    if (!user) return { error: "用户未登录" };
    try {
      const { data, error: error2 } = await supabase.from("chrono_entries").update(updates).eq("id", id).eq("user_id", user.id).select().single();
      if (error2) throw error2;
      setEntries((prev) => prev.map((entry2) => entry2.id === id ? data : entry2));
      return { data, error: null };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "更新记录失败";
      setError(errorMessage);
      return { error: errorMessage };
    }
  };
  const deleteEntry = async (id) => {
    if (!user) return { error: "用户未登录" };
    try {
      const { error: error2 } = await supabase.from("chrono_entries").delete().eq("id", id).eq("user_id", user.id);
      if (error2) throw error2;
      setEntries((prev) => prev.filter((entry2) => entry2.id !== id));
      return { error: null };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "删除记录失败";
      setError(errorMessage);
      return { error: errorMessage };
    }
  };
  const getEntry = async (id) => {
    if (!user) return { data: null, error: "用户未登录" };
    try {
      const { data, error: error2 } = await supabase.from("chrono_entries").select("*").eq("id", id).eq("user_id", user.id).single();
      if (error2) throw error2;
      return { data, error: null };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "获取记录失败";
      return { data: null, error: errorMessage };
    }
  };
  useEffect(() => {
    fetchEntries();
  }, [user]);
  return {
    entries,
    loading,
    error,
    createEntry,
    updateEntry,
    deleteEntry,
    getEntry,
    refetch: fetchEntries
  };
}
const journal = UNSAFE_withComponentProps(function JournalPage() {
  const {
    entries,
    loading,
    error,
    deleteEntry
  } = useChronoEntries();
  const [deletingId, setDeletingId] = useState(null);
  const {
    success,
    error: showError,
    ToastContainer
  } = useToast();
  const handleDelete = async (id) => {
    if (!confirm("确定要删除这条记录吗？此操作无法撤销。")) return;
    setDeletingId(id);
    const {
      error: error2
    } = await deleteEntry(id);
    if (error2) {
      showError("删除失败：" + error2);
    } else {
      success("记录已删除");
    }
    setDeletingId(null);
  };
  return /* @__PURE__ */ jsxs(Layout, {
    children: [/* @__PURE__ */ jsx(ToastContainer, {}), /* @__PURE__ */ jsxs("div", {
      className: "max-w-4xl mx-auto",
      children: [/* @__PURE__ */ jsxs("div", {
        className: "flex items-center justify-between mb-8",
        children: [/* @__PURE__ */ jsxs("div", {
          children: [/* @__PURE__ */ jsx("h1", {
            className: "text-3xl font-bold gradient-text mb-2",
            children: "时光书简"
          }), /* @__PURE__ */ jsx("p", {
            className: "text-gray-600",
            children: "记录珍贵回忆，创建专属的情感档案"
          })]
        }), /* @__PURE__ */ jsxs(Link, {
          to: "/journal/new",
          className: "flex items-center space-x-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-all transform hover:scale-105 shadow-lg btn-ripple",
          children: [/* @__PURE__ */ jsx(Plus, {
            className: "w-5 h-5"
          }), /* @__PURE__ */ jsx("span", {
            children: "新建记录"
          })]
        })]
      }), loading && /* @__PURE__ */ jsx("div", {
        className: "flex items-center justify-center py-12",
        children: /* @__PURE__ */ jsx(LoadingSpinner, {
          text: "加载记录中..."
        })
      }), error && /* @__PURE__ */ jsx("div", {
        className: "bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6 backdrop-blur-enhanced",
        children: error
      }), !loading && !error && entries.length === 0 && /* @__PURE__ */ jsx(EmptyState, {
        icon: BookOpen,
        title: "还没有任何记录",
        description: "开始记录你的第一个珍贵回忆吧",
        action: /* @__PURE__ */ jsxs(Link, {
          to: "/journal/new",
          className: "inline-flex items-center space-x-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-all transform hover:scale-105 btn-ripple",
          children: [/* @__PURE__ */ jsx(Plus, {
            className: "w-5 h-5"
          }), /* @__PURE__ */ jsx("span", {
            children: "创建第一条记录"
          })]
        })
      }), !loading && entries.length > 0 && /* @__PURE__ */ jsx("div", {
        className: "space-y-6",
        children: entries.map((entry2) => /* @__PURE__ */ jsxs("div", {
          className: "bg-white/80 backdrop-blur-enhanced rounded-2xl p-6 shadow-lg hover-lift",
          children: [/* @__PURE__ */ jsxs("div", {
            className: "flex items-start justify-between mb-4",
            children: [/* @__PURE__ */ jsxs("div", {
              className: "flex-1",
              children: [/* @__PURE__ */ jsx("h3", {
                className: "text-xl font-semibold text-gray-900 mb-2",
                children: entry2.title
              }), /* @__PURE__ */ jsxs("div", {
                className: "flex items-center space-x-4 text-sm text-gray-500",
                children: [/* @__PURE__ */ jsxs("div", {
                  className: "flex items-center space-x-1",
                  children: [/* @__PURE__ */ jsx(Calendar, {
                    className: "w-4 h-4"
                  }), /* @__PURE__ */ jsx("span", {
                    children: format(new Date(entry2.created_at), "yyyy年MM月dd日")
                  })]
                }), entry2.image_url && /* @__PURE__ */ jsxs("div", {
                  className: "flex items-center space-x-1",
                  children: [/* @__PURE__ */ jsx(Image, {
                    className: "w-4 h-4"
                  }), /* @__PURE__ */ jsx("span", {
                    children: "包含图片"
                  })]
                }), entry2.music_url && /* @__PURE__ */ jsxs("div", {
                  className: "flex items-center space-x-1",
                  children: [/* @__PURE__ */ jsx(Music, {
                    className: "w-4 h-4"
                  }), /* @__PURE__ */ jsx("span", {
                    children: "包含音乐"
                  })]
                })]
              })]
            }), /* @__PURE__ */ jsxs("div", {
              className: "flex items-center space-x-2",
              children: [/* @__PURE__ */ jsx(Link, {
                to: `/journal/${entry2.id}`,
                className: "p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",
                title: "查看详情",
                children: /* @__PURE__ */ jsx(Eye, {
                  className: "w-5 h-5"
                })
              }), /* @__PURE__ */ jsx(Link, {
                to: `/journal/${entry2.id}/edit`,
                className: "p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors",
                title: "编辑",
                children: /* @__PURE__ */ jsx(Edit, {
                  className: "w-5 h-5"
                })
              }), /* @__PURE__ */ jsx("button", {
                onClick: () => handleDelete(entry2.id),
                disabled: deletingId === entry2.id,
                className: clsx("p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors", deletingId === entry2.id && "opacity-50 cursor-not-allowed"),
                title: "删除",
                children: /* @__PURE__ */ jsx(Trash2, {
                  className: "w-5 h-5"
                })
              })]
            })]
          }), /* @__PURE__ */ jsx("p", {
            className: "text-gray-700 leading-relaxed line-clamp-3",
            children: entry2.content
          }), entry2.image_url && /* @__PURE__ */ jsx("div", {
            className: "mt-4",
            children: /* @__PURE__ */ jsx("img", {
              src: entry2.image_url,
              alt: "记录配图",
              className: "w-full h-48 object-cover rounded-lg"
            })
          })]
        }, entry2.id))
      })]
    })]
  });
});
const route3 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: journal
}, Symbol.toStringTag, { value: "Module" }));
function ProtectedRoute({ children }) {
  const { user, loading, error } = useAuthContext();
  if (loading) {
    return /* @__PURE__ */ jsx("div", { className: "min-h-screen flex items-center justify-center", children: /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
      /* @__PURE__ */ jsx("div", { className: "animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto mb-4" }),
      /* @__PURE__ */ jsx("p", { className: "text-gray-600", children: "正在验证身份..." })
    ] }) });
  }
  if (error) {
    return /* @__PURE__ */ jsx("div", { className: "min-h-screen flex items-center justify-center", children: /* @__PURE__ */ jsxs("div", { className: "text-center max-w-md mx-auto p-6", children: [
      /* @__PURE__ */ jsxs("div", { className: "bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4", children: [
        /* @__PURE__ */ jsx("p", { className: "text-yellow-800", children: "身份验证服务暂时不可用" }),
        /* @__PURE__ */ jsx("p", { className: "text-sm text-yellow-600 mt-2", children: error })
      ] }),
      /* @__PURE__ */ jsx(
        "button",
        {
          onClick: () => window.location.reload(),
          className: "bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",
          children: "重试"
        }
      )
    ] }) });
  }
  if (!user) {
    return /* @__PURE__ */ jsx(Navigate, { to: "/auth", replace: true });
  }
  return /* @__PURE__ */ jsx(Fragment, { children });
}
function JournalForm({ entry: entry2, isEdit = false }) {
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [imageUrl, setImageUrl] = useState("");
  const [musicUrl, setMusicUrl] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const { createEntry, updateEntry } = useChronoEntries();
  const navigate = useNavigate();
  useEffect(() => {
    if (entry2) {
      setTitle(entry2.title);
      setContent(entry2.content);
      setImageUrl(entry2.image_url || "");
      setMusicUrl(entry2.music_url || "");
    }
  }, [entry2]);
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!title.trim() || !content.trim()) {
      setError("标题和内容不能为空");
      return;
    }
    setLoading(true);
    setError("");
    const entryData = {
      title: title.trim(),
      content: content.trim(),
      image_url: imageUrl.trim() || void 0,
      music_url: musicUrl.trim() || void 0
    };
    let result;
    if (isEdit && entry2) {
      result = await updateEntry(entry2.id, entryData);
    } else {
      result = await createEntry(entryData);
    }
    if (result.error) {
      setError(result.error);
    } else {
      navigate("/journal");
    }
    setLoading(false);
  };
  const handleCancel = () => {
    navigate("/journal");
  };
  return /* @__PURE__ */ jsx("div", { className: "max-w-4xl mx-auto", children: /* @__PURE__ */ jsxs("div", { className: "bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl p-8", children: [
    /* @__PURE__ */ jsxs("div", { className: "mb-8", children: [
      /* @__PURE__ */ jsx("h1", { className: "text-3xl font-bold text-gray-900 mb-2", children: isEdit ? "编辑记录" : "创建新记录" }),
      /* @__PURE__ */ jsx("p", { className: "text-gray-600", children: isEdit ? "修改你的珍贵回忆" : "记录一个珍贵的回忆瞬间" })
    ] }),
    /* @__PURE__ */ jsxs("form", { onSubmit: handleSubmit, className: "space-y-6", children: [
      /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsx("label", { htmlFor: "title", className: "block text-sm font-medium text-gray-700 mb-2", children: "标题 *" }),
        /* @__PURE__ */ jsx(
          "input",
          {
            id: "title",
            type: "text",
            value: title,
            onChange: (e) => setTitle(e.target.value),
            className: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",
            placeholder: "为这个回忆起个标题...",
            required: true
          }
        )
      ] }),
      /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsx("label", { htmlFor: "content", className: "block text-sm font-medium text-gray-700 mb-2", children: "内容 *" }),
        /* @__PURE__ */ jsx(
          "textarea",
          {
            id: "content",
            value: content,
            onChange: (e) => setContent(e.target.value),
            rows: 12,
            className: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",
            placeholder: "写下你的回忆，记录那些珍贵的瞬间...",
            required: true
          }
        ),
        /* @__PURE__ */ jsxs("div", { className: "text-sm text-gray-500 mt-1", children: [
          content.length,
          " 字符"
        ] })
      ] }),
      /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsx("label", { htmlFor: "imageUrl", className: "block text-sm font-medium text-gray-700 mb-2", children: /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-2", children: [
          /* @__PURE__ */ jsx(Image, { className: "w-4 h-4" }),
          /* @__PURE__ */ jsx("span", { children: "图片链接（可选）" })
        ] }) }),
        /* @__PURE__ */ jsx(
          "input",
          {
            id: "imageUrl",
            type: "url",
            value: imageUrl,
            onChange: (e) => setImageUrl(e.target.value),
            className: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",
            placeholder: "https://example.com/image.jpg"
          }
        ),
        imageUrl && /* @__PURE__ */ jsx("div", { className: "mt-3", children: /* @__PURE__ */ jsx(
          "img",
          {
            src: imageUrl,
            alt: "预览",
            className: "w-full max-h-48 object-cover rounded-lg",
            onError: () => setError("图片链接无效")
          }
        ) })
      ] }),
      /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsx("label", { htmlFor: "musicUrl", className: "block text-sm font-medium text-gray-700 mb-2", children: /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-2", children: [
          /* @__PURE__ */ jsx(Music, { className: "w-4 h-4" }),
          /* @__PURE__ */ jsx("span", { children: "音乐链接（可选）" })
        ] }) }),
        /* @__PURE__ */ jsx(
          "input",
          {
            id: "musicUrl",
            type: "url",
            value: musicUrl,
            onChange: (e) => setMusicUrl(e.target.value),
            className: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",
            placeholder: "https://example.com/music.mp3"
          }
        ),
        /* @__PURE__ */ jsx("div", { className: "text-sm text-gray-500 mt-1", children: "支持 MP3、WAV 等音频格式" })
      ] }),
      error && /* @__PURE__ */ jsx("div", { className: "bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg", children: error }),
      /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-end space-x-4 pt-6 border-t border-gray-200", children: [
        /* @__PURE__ */ jsxs(
          "button",
          {
            type: "button",
            onClick: handleCancel,
            className: "flex items-center space-x-2 px-6 py-3 text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-lg transition-colors",
            children: [
              /* @__PURE__ */ jsx(X, { className: "w-5 h-5" }),
              /* @__PURE__ */ jsx("span", { children: "取消" })
            ]
          }
        ),
        /* @__PURE__ */ jsxs(
          "button",
          {
            type: "submit",
            disabled: loading,
            className: "flex items-center space-x-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",
            children: [
              /* @__PURE__ */ jsx(Save, { className: "w-5 h-5" }),
              /* @__PURE__ */ jsx("span", { children: loading ? "保存中..." : isEdit ? "更新记录" : "创建记录" })
            ]
          }
        )
      ] })
    ] })
  ] }) });
}
const journal_new = UNSAFE_withComponentProps(function NewJournalPage() {
  return /* @__PURE__ */ jsx(ProtectedRoute, {
    children: /* @__PURE__ */ jsx(Layout, {
      children: /* @__PURE__ */ jsx(JournalForm, {})
    })
  });
});
const route4 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: journal_new
}, Symbol.toStringTag, { value: "Module" }));
const journal_$id = UNSAFE_withComponentProps(function JournalDetailPage() {
  const {
    id
  } = useParams();
  const {
    getEntry
  } = useChronoEntries();
  const [entry2, setEntry] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audio, setAudio] = useState(null);
  useEffect(() => {
    const fetchEntry = async () => {
      if (!id) return;
      setLoading(true);
      const {
        data,
        error: error2
      } = await getEntry(id);
      if (error2) {
        setError(error2);
      } else {
        setEntry(data);
      }
      setLoading(false);
    };
    fetchEntry();
  }, [id, getEntry]);
  useEffect(() => {
    return () => {
      if (audio) {
        audio.pause();
        audio.src = "";
      }
    };
  }, [audio]);
  const handlePlayMusic = () => {
    if (!(entry2 == null ? void 0 : entry2.music_url)) return;
    if (audio) {
      if (isPlaying) {
        audio.pause();
        setIsPlaying(false);
      } else {
        audio.play();
        setIsPlaying(true);
      }
    } else {
      const newAudio = new Audio(entry2.music_url);
      newAudio.addEventListener("ended", () => setIsPlaying(false));
      newAudio.addEventListener("error", () => {
        setIsPlaying(false);
        alert("音乐播放失败");
      });
      setAudio(newAudio);
      newAudio.play();
      setIsPlaying(true);
    }
  };
  if (!id) {
    return /* @__PURE__ */ jsx(Navigate, {
      to: "/journal",
      replace: true
    });
  }
  return /* @__PURE__ */ jsx(ProtectedRoute, {
    children: /* @__PURE__ */ jsx(Layout, {
      children: /* @__PURE__ */ jsxs("div", {
        className: "max-w-4xl mx-auto",
        children: [/* @__PURE__ */ jsx("div", {
          className: "mb-6",
          children: /* @__PURE__ */ jsxs(Link, {
            to: "/journal",
            className: "inline-flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors",
            children: [/* @__PURE__ */ jsx(ArrowLeft, {
              className: "w-5 h-5"
            }), /* @__PURE__ */ jsx("span", {
              children: "返回书简列表"
            })]
          })
        }), loading && /* @__PURE__ */ jsx("div", {
          className: "flex items-center justify-center py-12",
          children: /* @__PURE__ */ jsx("div", {
            className: "animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"
          })
        }), error && /* @__PURE__ */ jsx("div", {
          className: "bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6",
          children: error
        }), entry2 && /* @__PURE__ */ jsxs("div", {
          className: "bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl overflow-hidden",
          children: [/* @__PURE__ */ jsxs("div", {
            className: "p-8 border-b border-gray-200",
            children: [/* @__PURE__ */ jsxs("div", {
              className: "flex items-start justify-between mb-4",
              children: [/* @__PURE__ */ jsx("h1", {
                className: "text-3xl font-bold text-gray-900",
                children: entry2.title
              }), /* @__PURE__ */ jsxs(Link, {
                to: `/journal/${entry2.id}/edit`,
                className: "flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",
                children: [/* @__PURE__ */ jsx(Edit, {
                  className: "w-4 h-4"
                }), /* @__PURE__ */ jsx("span", {
                  children: "编辑"
                })]
              })]
            }), /* @__PURE__ */ jsxs("div", {
              className: "flex items-center space-x-6 text-gray-600",
              children: [/* @__PURE__ */ jsxs("div", {
                className: "flex items-center space-x-2",
                children: [/* @__PURE__ */ jsx(Calendar, {
                  className: "w-5 h-5"
                }), /* @__PURE__ */ jsxs("span", {
                  children: ["创建于 ", format(new Date(entry2.created_at), "yyyy年MM月dd日 HH:mm")]
                })]
              }), entry2.updated_at !== entry2.created_at && /* @__PURE__ */ jsxs("div", {
                className: "text-sm",
                children: ["最后更新：", format(new Date(entry2.updated_at), "yyyy年MM月dd日 HH:mm")]
              })]
            })]
          }), /* @__PURE__ */ jsxs("div", {
            className: "p-8",
            children: [entry2.image_url && /* @__PURE__ */ jsx("div", {
              className: "mb-8",
              children: /* @__PURE__ */ jsx("img", {
                src: entry2.image_url,
                alt: "记录配图",
                className: "w-full max-h-96 object-cover rounded-lg shadow-lg"
              })
            }), entry2.music_url && /* @__PURE__ */ jsx("div", {
              className: "mb-8 p-4 bg-gray-50 rounded-lg",
              children: /* @__PURE__ */ jsxs("div", {
                className: "flex items-center space-x-4",
                children: [/* @__PURE__ */ jsx("button", {
                  onClick: handlePlayMusic,
                  className: "flex items-center justify-center w-12 h-12 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors",
                  children: isPlaying ? /* @__PURE__ */ jsx(Pause, {
                    className: "w-6 h-6"
                  }) : /* @__PURE__ */ jsx(Play, {
                    className: "w-6 h-6"
                  })
                }), /* @__PURE__ */ jsxs("div", {
                  className: "flex-1",
                  children: [/* @__PURE__ */ jsxs("div", {
                    className: "flex items-center space-x-2",
                    children: [/* @__PURE__ */ jsx(Music, {
                      className: "w-5 h-5 text-gray-600"
                    }), /* @__PURE__ */ jsx("span", {
                      className: "text-gray-700",
                      children: "背景音乐"
                    })]
                  }), /* @__PURE__ */ jsx("div", {
                    className: "text-sm text-gray-500",
                    children: isPlaying ? "正在播放..." : "点击播放"
                  })]
                })]
              })
            }), /* @__PURE__ */ jsx("div", {
              className: "prose prose-lg max-w-none",
              children: /* @__PURE__ */ jsx("div", {
                className: "text-gray-800 leading-relaxed whitespace-pre-wrap",
                children: entry2.content
              })
            })]
          })]
        })]
      })
    })
  });
});
const route5 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: journal_$id
}, Symbol.toStringTag, { value: "Module" }));
const journal_$id_edit = UNSAFE_withComponentProps(function EditJournalPage() {
  const {
    id
  } = useParams();
  const {
    getEntry
  } = useChronoEntries();
  const [entry2, setEntry] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  useEffect(() => {
    const fetchEntry = async () => {
      if (!id) return;
      setLoading(true);
      const {
        data,
        error: error2
      } = await getEntry(id);
      if (error2) {
        setError(error2);
      } else {
        setEntry(data);
      }
      setLoading(false);
    };
    fetchEntry();
  }, [id, getEntry]);
  if (!id) {
    return /* @__PURE__ */ jsx(Navigate, {
      to: "/journal",
      replace: true
    });
  }
  if (loading) {
    return /* @__PURE__ */ jsx(ProtectedRoute, {
      children: /* @__PURE__ */ jsx(Layout, {
        children: /* @__PURE__ */ jsx("div", {
          className: "flex items-center justify-center py-12",
          children: /* @__PURE__ */ jsx("div", {
            className: "animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"
          })
        })
      })
    });
  }
  if (error || !entry2) {
    return /* @__PURE__ */ jsx(ProtectedRoute, {
      children: /* @__PURE__ */ jsx(Layout, {
        children: /* @__PURE__ */ jsx("div", {
          className: "max-w-4xl mx-auto",
          children: /* @__PURE__ */ jsx("div", {
            className: "bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg",
            children: error || "记录不存在"
          })
        })
      })
    });
  }
  return /* @__PURE__ */ jsx(ProtectedRoute, {
    children: /* @__PURE__ */ jsx(Layout, {
      children: /* @__PURE__ */ jsx(JournalForm, {
        entry: entry2,
        isEdit: true
      })
    })
  });
});
const route6 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: journal_$id_edit
}, Symbol.toStringTag, { value: "Module" }));
function useEchoes() {
  const [echoes2, setEchoes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const fetchEchoes = async () => {
    try {
      setLoading(true);
      const { data, error: error2 } = await supabase.from("echoes").select("*").gt("expires_at", (/* @__PURE__ */ new Date()).toISOString()).order("created_at", { ascending: false });
      if (error2) throw error2;
      setEchoes(data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取回音失败");
    } finally {
      setLoading(false);
    }
  };
  const createEcho = async (content) => {
    try {
      if (content.length > 500) {
        return { error: "内容不能超过500字符" };
      }
      const { data, error: error2 } = await supabase.from("echoes").insert([{ content }]).select().single();
      if (error2) throw error2;
      setEchoes((prev) => [data, ...prev]);
      return { data, error: null };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "发布回音失败";
      setError(errorMessage);
      return { error: errorMessage };
    }
  };
  const getTimeRemaining = (expiresAt) => {
    const now = /* @__PURE__ */ new Date();
    const expiry = new Date(expiresAt);
    const diff = expiry.getTime() - now.getTime();
    if (diff <= 0) return "已过期";
    const hours = Math.floor(diff / (1e3 * 60 * 60));
    const minutes = Math.floor(diff % (1e3 * 60 * 60) / (1e3 * 60));
    if (hours > 0) {
      return `${hours}小时${minutes}分钟后消失`;
    } else {
      return `${minutes}分钟后消失`;
    }
  };
  useEffect(() => {
    fetchEchoes();
    const interval = setInterval(() => {
      setEchoes((prev) => prev.filter((echo) => new Date(echo.expires_at) > /* @__PURE__ */ new Date()));
    }, 6e4);
    return () => clearInterval(interval);
  }, []);
  return {
    echoes: echoes2,
    loading,
    error,
    createEcho,
    getTimeRemaining,
    refetch: fetchEchoes
  };
}
function EchoForm() {
  const [content, setContent] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const { createEcho } = useEchoes();
  const { success, error: showError } = useToast();
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!content.trim()) {
      setError("请输入内容");
      return;
    }
    if (content.length > 500) {
      setError("内容不能超过500字符");
      return;
    }
    setLoading(true);
    setError("");
    const { error: error2 } = await createEcho(content.trim());
    if (error2) {
      setError(error2);
      showError(error2);
    } else {
      setContent("");
      success("回音已投递到长廊中");
    }
    setLoading(false);
  };
  return /* @__PURE__ */ jsxs("div", { className: "bg-white/80 backdrop-blur-enhanced rounded-2xl p-6 shadow-lg mb-8 hover-lift", children: [
    /* @__PURE__ */ jsxs("div", { className: "mb-4", children: [
      /* @__PURE__ */ jsx("h3", { className: "text-lg font-semibold text-gray-900 mb-2", children: "投递一段回音" }),
      /* @__PURE__ */ jsxs("p", { className: "text-sm text-gray-600 flex items-center space-x-1", children: [
        /* @__PURE__ */ jsx(Clock, { className: "w-4 h-4" }),
        /* @__PURE__ */ jsx("span", { children: "匿名发布，24小时后自动消失" })
      ] })
    ] }),
    /* @__PURE__ */ jsxs("form", { onSubmit: handleSubmit, className: "space-y-4", children: [
      /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsx(
          "textarea",
          {
            value: content,
            onChange: (e) => setContent(e.target.value),
            rows: 4,
            className: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none",
            placeholder: "分享你的思绪、情感或感悟...如漫天蝶游般自由表达",
            maxLength: 500
          }
        ),
        /* @__PURE__ */ jsxs("div", { className: "flex justify-between items-center mt-2", children: [
          /* @__PURE__ */ jsxs("div", { className: "text-sm text-gray-500", children: [
            content.length,
            "/500 字符"
          ] }),
          content.length > 450 && /* @__PURE__ */ jsx("div", { className: "text-sm text-orange-600", children: "即将达到字符限制" })
        ] })
      ] }),
      error && /* @__PURE__ */ jsx("div", { className: "bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm", children: error }),
      /* @__PURE__ */ jsx("div", { className: "flex justify-end", children: /* @__PURE__ */ jsxs(
        "button",
        {
          type: "submit",
          disabled: loading || !content.trim(),
          className: "flex items-center space-x-2 bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all transform hover:scale-105 btn-ripple",
          children: [
            /* @__PURE__ */ jsx(Send, { className: "w-5 h-5" }),
            /* @__PURE__ */ jsx("span", { children: loading ? "发布中..." : "投递回音" })
          ]
        }
      ) })
    ] })
  ] });
}
function EchoCard({ echo }) {
  const { getTimeRemaining } = useEchoes();
  const [timeRemaining, setTimeRemaining] = useState("");
  const [isLiked, setIsLiked] = useState(false);
  useEffect(() => {
    const updateTime = () => {
      setTimeRemaining(getTimeRemaining(echo.expires_at));
    };
    updateTime();
    const interval = setInterval(updateTime, 6e4);
    return () => clearInterval(interval);
  }, [echo.expires_at, getTimeRemaining]);
  const handleLike = () => {
    setIsLiked(!isLiked);
  };
  const getRandomGradient = () => {
    const gradients = [
      "from-purple-400 to-pink-400",
      "from-blue-400 to-purple-400",
      "from-pink-400 to-red-400",
      "from-indigo-400 to-blue-400",
      "from-purple-400 to-indigo-400",
      "from-pink-400 to-purple-400"
    ];
    const hash = echo.id.split("").reduce((a, b) => {
      a = (a << 5) - a + b.charCodeAt(0);
      return a & a;
    }, 0);
    return gradients[Math.abs(hash) % gradients.length];
  };
  return /* @__PURE__ */ jsxs("div", { className: "group relative", children: [
    /* @__PURE__ */ jsx("div", { className: clsx(
      "absolute inset-0 bg-gradient-to-br opacity-10 rounded-2xl transform rotate-1 group-hover:rotate-2 transition-transform",
      getRandomGradient()
    ) }),
    /* @__PURE__ */ jsxs("div", { className: "relative bg-white/80 backdrop-blur-enhanced rounded-2xl p-6 shadow-lg hover-lift", children: [
      /* @__PURE__ */ jsx("div", { className: "mb-4", children: /* @__PURE__ */ jsx("p", { className: "text-gray-800 leading-relaxed whitespace-pre-wrap", children: echo.content }) }),
      /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between text-sm", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-4 text-gray-500", children: [
          /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-1", children: [
            /* @__PURE__ */ jsx(Clock, { className: "w-4 h-4" }),
            /* @__PURE__ */ jsx("span", { children: timeRemaining })
          ] }),
          /* @__PURE__ */ jsx("div", { children: format(new Date(echo.created_at), "MM月dd日 HH:mm") })
        ] }),
        /* @__PURE__ */ jsxs(
          "button",
          {
            onClick: handleLike,
            className: clsx(
              "flex items-center space-x-1 px-3 py-1 rounded-full transition-colors",
              isLiked ? "bg-red-100 text-red-600" : "text-gray-500 hover:bg-gray-100 hover:text-red-500"
            ),
            children: [
              /* @__PURE__ */ jsx(Heart, { className: clsx("w-4 h-4", isLiked && "fill-current") }),
              /* @__PURE__ */ jsx("span", { className: "text-xs", children: "共鸣" })
            ]
          }
        )
      ] }),
      /* @__PURE__ */ jsx("div", { className: "absolute top-4 right-4 opacity-20", children: /* @__PURE__ */ jsx("div", { className: "w-8 h-8 rounded-full bg-gradient-to-br from-purple-400 to-pink-400 animate-pulse" }) })
    ] })
  ] });
}
const echoes = UNSAFE_withComponentProps(function EchoesPage() {
  const {
    echoes: echoes2,
    loading,
    error,
    refetch
  } = useEchoes();
  return /* @__PURE__ */ jsx(Layout, {
    children: /* @__PURE__ */ jsxs("div", {
      className: "max-w-4xl mx-auto",
      children: [/* @__PURE__ */ jsxs("div", {
        className: "text-center mb-8",
        children: [/* @__PURE__ */ jsxs("div", {
          className: "flex items-center justify-center space-x-3 mb-4",
          children: [/* @__PURE__ */ jsx(MessageCircle, {
            className: "w-8 h-8 text-purple-600 animate-float"
          }), /* @__PURE__ */ jsx("h1", {
            className: "text-3xl font-bold gradient-text",
            children: "回音长廊"
          }), /* @__PURE__ */ jsx(Sparkles, {
            className: "w-8 h-8 text-purple-600 animate-float",
            style: {
              animationDelay: "1s"
            }
          })]
        }), /* @__PURE__ */ jsx("p", {
          className: "text-gray-600 max-w-2xl mx-auto leading-relaxed",
          children: '在这里匿名分享你的思绪与情感，如"漫天蝶游"般自由表达。 每一段回音都会在24小时后消失，象征着情感的流逝。 谁不是流泪的旁观者，在无声的共鸣中获得慰藉。'
        })]
      }), /* @__PURE__ */ jsx(EchoForm, {}), /* @__PURE__ */ jsx("div", {
        className: "flex justify-center mb-6",
        children: /* @__PURE__ */ jsxs("button", {
          onClick: refetch,
          disabled: loading,
          className: "flex items-center space-x-2 text-gray-600 hover:text-purple-600 transition-colors",
          children: [/* @__PURE__ */ jsx(RefreshCw, {
            className: `w-5 h-5 ${loading ? "animate-spin" : ""}`
          }), /* @__PURE__ */ jsx("span", {
            children: "刷新回音"
          })]
        })
      }), loading && /* @__PURE__ */ jsx("div", {
        className: "flex items-center justify-center py-12",
        children: /* @__PURE__ */ jsx(LoadingSpinner, {
          color: "purple",
          text: "聆听回音中..."
        })
      }), error && /* @__PURE__ */ jsx("div", {
        className: "bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6 backdrop-blur-enhanced",
        children: error
      }), !loading && !error && echoes2.length === 0 && /* @__PURE__ */ jsx(EmptyState, {
        icon: MessageCircle,
        title: "长廊中还很安静",
        description: "成为第一个投递回音的人吧",
        className: "bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl"
      }), !loading && echoes2.length > 0 && /* @__PURE__ */ jsxs("div", {
        className: "space-y-6",
        children: [/* @__PURE__ */ jsx("div", {
          className: "text-center mb-6",
          children: /* @__PURE__ */ jsxs("p", {
            className: "text-gray-600",
            children: ["共有 ", /* @__PURE__ */ jsx("span", {
              className: "font-semibold text-purple-600",
              children: echoes2.length
            }), " 段回音在长廊中回响"]
          })
        }), /* @__PURE__ */ jsx("div", {
          className: "grid gap-6",
          children: echoes2.map((echo) => /* @__PURE__ */ jsx(EchoCard, {
            echo
          }, echo.id))
        })]
      }), /* @__PURE__ */ jsxs("div", {
        className: "text-center mt-12 py-8 border-t border-gray-200",
        children: [/* @__PURE__ */ jsx("blockquote", {
          className: "text-lg text-gray-700 italic leading-relaxed mb-2",
          children: '"物是人非谁不是流泪的旁观者"'
        }), /* @__PURE__ */ jsx("p", {
          className: "text-gray-500",
          children: "— 在共鸣中寻找慰藉"
        })]
      })]
    })
  });
});
const route7 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: echoes
}, Symbol.toStringTag, { value: "Module" }));
const serverManifest = { "entry": { "module": "/assets/entry.client-BZJYblgB.js", "imports": ["/assets/chunk-QMGIS6GS-Dm1jN8z9.js"], "css": [] }, "routes": { "root": { "id": "root", "parentId": void 0, "path": "", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasClientMiddleware": false, "hasErrorBoundary": true, "module": "/assets/root-DqH40PaC.js", "imports": ["/assets/chunk-QMGIS6GS-Dm1jN8z9.js", "/assets/AuthContext-C3ETFuYa.js"], "css": ["/assets/root-TIUnAXh3.css"], "clientActionModule": void 0, "clientLoaderModule": void 0, "clientMiddlewareModule": void 0, "hydrateFallbackModule": void 0 }, "routes/home": { "id": "routes/home", "parentId": "root", "path": void 0, "index": true, "caseSensitive": void 0, "hasAction": false, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasClientMiddleware": false, "hasErrorBoundary": false, "module": "/assets/home-D0wFvBrQ.js", "imports": ["/assets/chunk-QMGIS6GS-Dm1jN8z9.js", "/assets/AuthContext-C3ETFuYa.js", "/assets/Layout-B-k2_PuX.js", "/assets/heart-COttkdNw.js", "/assets/user-BOgs6E2m.js"], "css": [], "clientActionModule": void 0, "clientLoaderModule": void 0, "clientMiddlewareModule": void 0, "hydrateFallbackModule": void 0 }, "routes/auth": { "id": "routes/auth", "parentId": "root", "path": "auth", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasClientMiddleware": false, "hasErrorBoundary": false, "module": "/assets/auth-DHrXSA_2.js", "imports": ["/assets/chunk-QMGIS6GS-Dm1jN8z9.js", "/assets/AuthContext-C3ETFuYa.js", "/assets/user-BOgs6E2m.js", "/assets/eye-BYrV7Kom.js"], "css": [], "clientActionModule": void 0, "clientLoaderModule": void 0, "clientMiddlewareModule": void 0, "hydrateFallbackModule": void 0 }, "routes/journal": { "id": "routes/journal", "parentId": "root", "path": "journal", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasClientMiddleware": false, "hasErrorBoundary": false, "module": "/assets/journal-QrNN0Swy.js", "imports": ["/assets/chunk-QMGIS6GS-Dm1jN8z9.js", "/assets/Layout-B-k2_PuX.js", "/assets/Toast-DTYeq3xB.js", "/assets/useChronoEntries-DsUNOhE7.js", "/assets/user-BOgs6E2m.js", "/assets/square-pen-BTbZEIlQ.js", "/assets/format-CBpsKyOP.js", "/assets/image-CJpBvlEq.js", "/assets/eye-BYrV7Kom.js", "/assets/AuthContext-C3ETFuYa.js"], "css": [], "clientActionModule": void 0, "clientLoaderModule": void 0, "clientMiddlewareModule": void 0, "hydrateFallbackModule": void 0 }, "routes/journal.new": { "id": "routes/journal.new", "parentId": "root", "path": "journal/new", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasClientMiddleware": false, "hasErrorBoundary": false, "module": "/assets/journal.new-YBx_UvoQ.js", "imports": ["/assets/chunk-QMGIS6GS-Dm1jN8z9.js", "/assets/Layout-B-k2_PuX.js", "/assets/ProtectedRoute-BQ7DlOLV.js", "/assets/JournalForm-C-QerN0r.js", "/assets/AuthContext-C3ETFuYa.js", "/assets/user-BOgs6E2m.js", "/assets/useChronoEntries-DsUNOhE7.js", "/assets/image-CJpBvlEq.js"], "css": [], "clientActionModule": void 0, "clientLoaderModule": void 0, "clientMiddlewareModule": void 0, "hydrateFallbackModule": void 0 }, "routes/journal.$id": { "id": "routes/journal.$id", "parentId": "root", "path": "journal/:id", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasClientMiddleware": false, "hasErrorBoundary": false, "module": "/assets/journal._id-CPyOb8Qx.js", "imports": ["/assets/chunk-QMGIS6GS-Dm1jN8z9.js", "/assets/Layout-B-k2_PuX.js", "/assets/ProtectedRoute-BQ7DlOLV.js", "/assets/useChronoEntries-DsUNOhE7.js", "/assets/AuthContext-C3ETFuYa.js", "/assets/user-BOgs6E2m.js", "/assets/square-pen-BTbZEIlQ.js", "/assets/format-CBpsKyOP.js"], "css": [], "clientActionModule": void 0, "clientLoaderModule": void 0, "clientMiddlewareModule": void 0, "hydrateFallbackModule": void 0 }, "routes/journal.$id.edit": { "id": "routes/journal.$id.edit", "parentId": "root", "path": "journal/:id/edit", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasClientMiddleware": false, "hasErrorBoundary": false, "module": "/assets/journal._id.edit-BfnzvVrM.js", "imports": ["/assets/chunk-QMGIS6GS-Dm1jN8z9.js", "/assets/Layout-B-k2_PuX.js", "/assets/ProtectedRoute-BQ7DlOLV.js", "/assets/JournalForm-C-QerN0r.js", "/assets/useChronoEntries-DsUNOhE7.js", "/assets/AuthContext-C3ETFuYa.js", "/assets/user-BOgs6E2m.js", "/assets/image-CJpBvlEq.js"], "css": [], "clientActionModule": void 0, "clientLoaderModule": void 0, "clientMiddlewareModule": void 0, "hydrateFallbackModule": void 0 }, "routes/echoes": { "id": "routes/echoes", "parentId": "root", "path": "echoes", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasClientMiddleware": false, "hasErrorBoundary": false, "module": "/assets/echoes-DLmejbw5.js", "imports": ["/assets/chunk-QMGIS6GS-Dm1jN8z9.js", "/assets/Layout-B-k2_PuX.js", "/assets/Toast-DTYeq3xB.js", "/assets/AuthContext-C3ETFuYa.js", "/assets/user-BOgs6E2m.js", "/assets/heart-COttkdNw.js", "/assets/format-CBpsKyOP.js"], "css": [], "clientActionModule": void 0, "clientLoaderModule": void 0, "clientMiddlewareModule": void 0, "hydrateFallbackModule": void 0 } }, "url": "/assets/manifest-e3ff4b86.js", "version": "e3ff4b86", "sri": void 0 };
const assetsBuildDirectory = "build\\client";
const basename = "/";
const future = { "unstable_middleware": false, "unstable_optimizeDeps": false, "unstable_splitRouteModules": false, "unstable_subResourceIntegrity": false, "unstable_viteEnvironmentApi": false };
const ssr = true;
const isSpaMode = false;
const prerender = [];
const routeDiscovery = { "mode": "lazy", "manifestPath": "/__manifest" };
const publicPath = "/";
const entry = { module: entryServer };
const routes = {
  "root": {
    id: "root",
    parentId: void 0,
    path: "",
    index: void 0,
    caseSensitive: void 0,
    module: route0
  },
  "routes/home": {
    id: "routes/home",
    parentId: "root",
    path: void 0,
    index: true,
    caseSensitive: void 0,
    module: route1
  },
  "routes/auth": {
    id: "routes/auth",
    parentId: "root",
    path: "auth",
    index: void 0,
    caseSensitive: void 0,
    module: route2
  },
  "routes/journal": {
    id: "routes/journal",
    parentId: "root",
    path: "journal",
    index: void 0,
    caseSensitive: void 0,
    module: route3
  },
  "routes/journal.new": {
    id: "routes/journal.new",
    parentId: "root",
    path: "journal/new",
    index: void 0,
    caseSensitive: void 0,
    module: route4
  },
  "routes/journal.$id": {
    id: "routes/journal.$id",
    parentId: "root",
    path: "journal/:id",
    index: void 0,
    caseSensitive: void 0,
    module: route5
  },
  "routes/journal.$id.edit": {
    id: "routes/journal.$id.edit",
    parentId: "root",
    path: "journal/:id/edit",
    index: void 0,
    caseSensitive: void 0,
    module: route6
  },
  "routes/echoes": {
    id: "routes/echoes",
    parentId: "root",
    path: "echoes",
    index: void 0,
    caseSensitive: void 0,
    module: route7
  }
};
export {
  serverManifest as assets,
  assetsBuildDirectory,
  basename,
  entry,
  future,
  isSpaMode,
  prerender,
  publicPath,
  routeDiscovery,
  routes,
  ssr
};
