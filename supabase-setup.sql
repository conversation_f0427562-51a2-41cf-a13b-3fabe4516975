-- 梦的出口 - 数据库表结构
-- 请在 Supabase SQL Editor 中执行此脚本

-- 注意: JWT secret 应在 Supabase Dashboard 的项目设置中配置，而不是通过 SQL 设置

-- 1. 创建时光书简表 (Chronoscroll)
CREATE TABLE IF NOT EXISTS chrono_entries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    image_url TEXT,
    music_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. 创建回音长廊表 (Echo Gallery)
CREATE TABLE IF NOT EXISTS echoes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    content TEXT NOT NULL CHECK (char_length(content) <= 500),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '24 hours')
);

-- 4. 创建用户配置表
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    display_name VARCHAR(50),
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. 启用 RLS 策略
ALTER TABLE chrono_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE echoes ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- 3. 创建 RLS 策略

-- 时光书简策略：用户只能访问自己的记录
CREATE POLICY "Users can view own chrono entries" ON chrono_entries
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own chrono entries" ON chrono_entries
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own chrono entries" ON chrono_entries
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own chrono entries" ON chrono_entries
    FOR DELETE USING (auth.uid() = user_id);

-- 回音长廊策略：所有人都可以查看未过期的回音，任何人都可以发布
CREATE POLICY "Anyone can view unexpired echoes" ON echoes
    FOR SELECT USING (expires_at > NOW());

CREATE POLICY "Anyone can insert echoes" ON echoes
    FOR INSERT WITH CHECK (true);

-- 用户配置策略：用户只能访问自己的配置
CREATE POLICY "Users can view own profile" ON user_profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON user_profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON user_profiles
    FOR UPDATE USING (auth.uid() = id);

-- 4. 创建函数：自动删除过期的回音
CREATE OR REPLACE FUNCTION delete_expired_echoes()
RETURNS void AS $$
BEGIN
    DELETE FROM echoes WHERE expires_at <= NOW();
END;
$$ LANGUAGE plpgsql;

-- 5. 创建定时任务（需要在 Supabase Dashboard 中手动设置 cron job）
-- SELECT cron.schedule('delete-expired-echoes', '0 * * * *', 'SELECT delete_expired_echoes();');

-- 6. 创建触发器：自动更新 updated_at 字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_chrono_entries_updated_at
    BEFORE UPDATE ON chrono_entries
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_profiles_updated_at
    BEFORE UPDATE ON user_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 7. 创建用户注册时自动创建 profile 的触发器
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
    INSERT INTO public.user_profiles (id, display_name)
    VALUES (new.id, new.email);
    RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- 8. 创建存储桶（用于文件上传）
-- 这部分需要在 Supabase Storage 中手动创建
-- 存储桶名称：
-- - chrono-images (用于时光书简的图片)
-- - chrono-music (用于时光书简的音乐文件)
